// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cash_box.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CashBox _$CashBoxFromJson(Map<String, dynamic> json) => CashBox(
      id: json['id'] as String,
      name: json['name'] as String,
      balance: (json['balance'] as num?)?.toDouble() ?? 0.0,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      deletedAt: json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
      branchId: json['branch_id'] as String?,
      isSynced: json['is_synced'] as bool? ?? false,
    );

Map<String, dynamic> _$CashBoxToJson(CashBox instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'balance': instance.balance,
      'is_active': instance.isActive,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'deleted_at': instance.deletedAt?.toIso8601String(),
      'branch_id': instance.branchId,
      'is_synced': instance.isSynced,
    };
