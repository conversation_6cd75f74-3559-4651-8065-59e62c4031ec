
// import 'package:sqflite/sqflite.dart';
// import '../../../core/constants/database_constants.dart';
// import '../../../core/utils/app_utils.dart';
// import '../../../core/utils/error_handler.dart';
// import '../database.dart';
// import '../../models/account.dart';

// /// DAO موحد للعملاء والموردين
// /// Unified DAO for Customers and Suppliers


// /// DAO موحد للعملاء والموردين
// /// Unified DAO for Customers and Suppliers
// class AccountDao {
//   final DatabaseHelper _databaseHelper;
//   AccountDao(this._databaseHelper);

//   /// الحصول على قاعدة البيانات مع معالجة الأخطاء المحتملة
//   Future<Database> get database async {
//     try {
//       final db = await _databaseHelper.database;
//       AppUtils.logInfo('Database access successful in AccountDao');
//       return db;
//     } catch (e) {
//       AppUtils.logError('Database access failed in AccountDao', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           AppUtils.logInfo('تم إعادة تعيين قاعدة البيانات بنجاح');
//           return db;
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات', resetError);
//           rethrow;
//         }
//       }
//       rethrow;
//     }
//   }

//   /// الحصول على اسم الجدول حسب نوع الحساب
//   String _getTableName(AccountType type) {
//     switch (type) {
//       case AccountType.customer:
//         return DatabaseConstants.tableCustomers;
//       case AccountType.supplier:
//         return DatabaseConstants.tableSuppliers;
//     }
//   }

//   /// الحصول على أسماء الأعمدة حسب نوع الحساب
//   Map<String, String> _getColumnNames(AccountType type) {
//     switch (type) {
//       case AccountType.customer:
//         return {
//           'id': DatabaseConstants.columnCustomerId,
//           'name': DatabaseConstants.columnCustomerName,
//           'phone': DatabaseConstants.columnCustomerPhone,
//           'email': DatabaseConstants.columnCustomerEmail,
//           'address': DatabaseConstants.columnCustomerAddress,
//           'balance': DatabaseConstants.columnCustomerBalance,
//           'notes': DatabaseConstants.columnCustomerNotes,
//           'created_at': DatabaseConstants.columnCustomerCreatedAt,
//           'updated_at': DatabaseConstants.columnCustomerUpdatedAt,
//           'deleted_at': DatabaseConstants.columnCustomerDeletedAt,
//           'is_synced': DatabaseConstants.columnCustomerIsSynced,
//         };
//       case AccountType.supplier:
//         return {
//           'id': DatabaseConstants.columnSupplierId,
//           'name': DatabaseConstants.columnSupplierName,
//           'phone': DatabaseConstants.columnSupplierPhone,
//           'email': DatabaseConstants.columnSupplierEmail,
//           'address': DatabaseConstants.columnSupplierAddress,
//           'balance': DatabaseConstants.columnSupplierBalance,
//           'notes': DatabaseConstants.columnSupplierNotes,
//           'created_at': DatabaseConstants.columnSupplierCreatedAt,
//           'updated_at': DatabaseConstants.columnSupplierUpdatedAt,
//           'deleted_at': DatabaseConstants.columnSupplierDeletedAt,
//           'is_synced': DatabaseConstants.columnSupplierIsSynced,
//         };
//     }
//   }

//   /// تحويل Account إلى Map للحفظ في قاعدة البيانات
//   Map<String, dynamic> _accountToMap(Account account) {
//     final columns = _getColumnNames(account.type);
//     return {
//       columns['id']!: account.id,
//       columns['name']!: account.name,
//       columns['phone']!: account.phone,
//       columns['email']!: account.email,
//       columns['address']!: account.address,
//       columns['balance']!: account.balance,
//       columns['notes']!: account.notes,
//       columns['created_at']!: account.createdAt?.toIso8601String(),
//       columns['updated_at']!: account.updatedAt?.toIso8601String(),
//       columns['deleted_at']!: account.deletedAt?.toIso8601String(),
//       columns['is_synced']!: account.isSynced ? 1 : 0,
//     };
//   }

//   /// تحويل Map من قاعدة البيانات إلى Account
//   Account _mapToAccount(Map<String, dynamic> map, AccountType type) {
//     // إضافة معلومة نوع الجدول للمساعدة في التحويل
//     map['_table_name'] = _getTableName(type);
//     return Account.fromMap(map);
//   }

//   // ------------------------------------------------------------------
//   // العمليات الأساسية - Basic Operations
//   // ------------------------------------------------------------------

//   /// إنشاء حساب جديد
//   Future<String> insert(Account account) async {
//     try {
//       AppUtils.logInfo('بدء إدراج ${account.type.displayName}: ${account.name}');
//       final db = await database; // استخدام getter المحسن
//       final tableName = _getTableName(account.type);
//       final map = _accountToMap(account);

//       // إضافة الطوابع الزمنية
//       final now = DateTime.now().toIso8601String();
//       map[_getColumnNames(account.type)['created_at']!] = now;
//       map[_getColumnNames(account.type)['updated_at']!] = now;
//       map[_getColumnNames(account.type)['is_synced']!] = 0;

//       // توليد ID إذا لم يكن موجوداً
//       if (map[_getColumnNames(account.type)['id']!] == null ||
//           map[_getColumnNames(account.type)['id']!].toString().isEmpty) {
//         map[_getColumnNames(account.type)['id']!] = AppUtils.generateId();
//       }

//       AppUtils.logInfo('إدراج في الجدول: $tableName');
//       AppUtils.logInfo('البيانات: ${map.keys.join(', ')}');

//       await db.insert(tableName, map);
//       AppUtils.logInfo('تم إدراج ${account.type.displayName} بنجاح - ID: ${map[_getColumnNames(account.type)['id']!]}');
//       return map[_getColumnNames(account.type)['id']];
//     } catch (e) {
//       AppUtils.logError('خطأ في إدراج ${account.type.displayName}', e);
//       rethrow; // إعادة رمي الخطأ الأصلي بدون تعديل
//     }
//   }

//   /// تحديث حساب موجود
//   Future<void> update(Account account) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(account.type);
//       final columns = _getColumnNames(account.type);
//       final map = _accountToMap(account);

//       // تحديث الطابع الزمني
//       map[columns['updated_at']!] = DateTime.now().toIso8601String();
//       map[columns['is_synced']!] = 0;

//       final rowsAffected = await db.update(
//         tableName,
//         map,
//         where: '${columns['id']!} = ?',
//         whereArgs: [account.id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception('لم يتم العثور على ${account.type.displayName} للتحديث');
//       }

//       AppUtils.logInfo('تم تحديث ${account.type.displayName} بنجاح - ID: ${account.id}');
//     } catch (e) {
//       AppUtils.logError('خطأ في تحديث ${account.type.displayName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة تحديث الحساب...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(account.type);
//           final columns = _getColumnNames(account.type);
//           final map = _accountToMap(account);

//           // تحديث الطابع الزمني
//           map[columns['updated_at']!] = DateTime.now().toIso8601String();
//           map[columns['is_synced']!] = 0;

//           final rowsAffected = await db.update(
//             tableName,
//             map,
//             where: '${columns['id']!} = ?',
//             whereArgs: [account.id],
//           );

//           if (rowsAffected == 0) {
//             throw Exception('لم يتم العثور على ${account.type.displayName} للتحديث');
//           }

//           AppUtils.logInfo('تم تحديث ${account.type.displayName} بنجاح بعد إعادة تعيين قاعدة البيانات - ID: ${account.id}');
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات وتحديث الحساب', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// حذف حساب (حذف ناعم)
//   Future<void> delete(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final rowsAffected = await db.update(
//         tableName,
//         {
//           columns['deleted_at']!: DateTime.now().toIso8601String(),
//           columns['updated_at']!: DateTime.now().toIso8601String(),
//           columns['is_synced']!: 0,
//         },
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception('لم يتم العثور على ${type.displayName} للحذف');
//       }

//       AppUtils.logInfo('تم حذف ${type.displayName} بنجاح - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في حذف ${type.displayName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة حذف الحساب...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           final rowsAffected = await db.update(
//             tableName,
//             {
//               columns['deleted_at']!: DateTime.now().toIso8601String(),
//               columns['updated_at']!: DateTime.now().toIso8601String(),
//               columns['is_synced']!: 0,
//             },
//             where: '${columns['id']!} = ?',
//             whereArgs: [id],
//           );

//           if (rowsAffected == 0) {
//             throw Exception('لم يتم العثور على ${type.displayName} للحذف');
//           }

//           AppUtils.logInfo('تم حذف ${type.displayName} بنجاح بعد إعادة تعيين قاعدة البيانات - ID: $id');
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات وحذف الحساب', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// حذف حساب نهائياً
//   Future<void> deleteForever(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final rowsAffected = await db.delete(
//         tableName,
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception('لم يتم العثور على ${type.displayName} للحذف النهائي');
//       }

//       AppUtils.logInfo('تم حذف ${type.displayName} نهائياً - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في الحذف النهائي لـ ${type.displayName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة الحذف النهائي...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           final rowsAffected = await db.delete(
//             tableName,
//             where: '${columns['id']!} = ?',
//             whereArgs: [id],
//           );

//           if (rowsAffected == 0) {
//             throw Exception('لم يتم العثور على ${type.displayName} للحذف النهائي');
//           }

//           AppUtils.logInfo('تم حذف ${type.displayName} نهائياً بعد إعادة تعيين قاعدة البيانات - ID: $id');
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات والحذف النهائي', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// البحث عن حساب بالمعرف
//   Future<Account?> findById(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//         limit: 1,
//       );

//       if (maps.isEmpty) return null;
//       return _mapToAccount(maps.first, type);
//     } catch (e) {
//       AppUtils.logError('خطأ في البحث عن ${type.displayName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة البحث...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           final List<Map<String, dynamic>> maps = await db.query(
//             tableName,
//             where: '${columns['id']!} = ?',
//             whereArgs: [id],
//             limit: 1,
//           );

//           if (maps.isEmpty) return null;
//           return _mapToAccount(maps.first, type);
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات والبحث', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// الحصول على جميع الحسابات من نوع معين
//   Future<List<Account>> findAll(AccountType type, {bool includeDeleted = false}) async {
//     try {
//       AppUtils.logInfo('بدء استرجاع ${type.pluralName}');
//       final db = await database; // استخدام getter المحسن
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String? where;
//       if (!includeDeleted) {
//         where = '${columns['deleted_at']!} IS NULL';
//       }

//       AppUtils.logInfo('استعلام من الجدول: $tableName');

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         orderBy: columns['name']!,
//       );

//       AppUtils.logInfo('تم استرجاع ${maps.length} من ${type.pluralName}');
//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة استرجاع البيانات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           String? where;
//           if (!includeDeleted) {
//             where = '${columns['deleted_at']!} IS NULL';
//           }

//           final List<Map<String, dynamic>> maps = await db.query(
//             tableName,
//             where: where,
//             orderBy: columns['name']!,
//           );

//           AppUtils.logInfo('تم استرجاع ${maps.length} من ${type.pluralName} بعد إعادة تعيين قاعدة البيانات');
//           return maps.map((map) => _mapToAccount(map, type)).toList();
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات واسترجاع البيانات', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         rethrow;
//       }
//     }
//   }

//   /// البحث في الحسابات
//   Future<List<Account>> search(
//     AccountType type,
//     String query, {
//     bool includeDeleted = false,
//   }) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String where = '(${columns['name']!} LIKE ? OR ${columns['phone']!} LIKE ? OR ${columns['email']!} LIKE ?)';
//       List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

//       if (!includeDeleted) {
//         where += ' AND ${columns['deleted_at']!} IS NULL';
//       }

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         whereArgs: whereArgs,
//         orderBy: columns['name']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في البحث في ${type.pluralName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة البحث...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           String where = '(${columns['name']!} LIKE ? OR ${columns['phone']!} LIKE ? OR ${columns['email']!} LIKE ?)';
//           List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];

//           if (!includeDeleted) {
//             where += ' AND ${columns['deleted_at']!} IS NULL';
//           }

//           final List<Map<String, dynamic>> maps = await db.query(
//             tableName,
//             where: where,
//             whereArgs: whereArgs,
//             orderBy: columns['name']!,
//           );

//           return maps.map((map) => _mapToAccount(map, type)).toList();
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات والبحث', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// الحصول على الحسابات حسب حالة الرصيد
//   Future<List<Account>> findByBalanceStatus(
//     AccountType type,
//     BalanceStatus status, {
//     bool includeDeleted = false,
//   }) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String balanceCondition;
//       switch (status) {
//         case BalanceStatus.debt:
//           balanceCondition = '${columns['balance']!} > 0';
//           break;
//         case BalanceStatus.credit:
//           balanceCondition = '${columns['balance']!} < 0';
//           break;
//         case BalanceStatus.balanced:
//           balanceCondition = '${columns['balance']!} = 0';
//           break;
//       }

//       String where = balanceCondition;
//       if (!includeDeleted) {
//         where += ' AND ${columns['deleted_at']!} IS NULL';
//       }

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         orderBy: columns['name']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName} حسب حالة الرصيد', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة استرجاع البيانات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           String balanceCondition;
//           switch (status) {
//             case BalanceStatus.debt:
//               balanceCondition = '${columns['balance']!} > 0';
//               break;
//             case BalanceStatus.credit:
//               balanceCondition = '${columns['balance']!} < 0';
//               break;
//             case BalanceStatus.balanced:
//               balanceCondition = '${columns['balance']!} = 0';
//               break;
//           }

//           String where = balanceCondition;
//           if (!includeDeleted) {
//             where += ' AND ${columns['deleted_at']!} IS NULL';
//           }

//           final List<Map<String, dynamic>> maps = await db.query(
//             tableName,
//             where: where,
//             orderBy: columns['name']!,
//           );

//           return maps.map((map) => _mapToAccount(map, type)).toList();
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات واسترجاع البيانات', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// الحصول على إحصائيات الحسابات
//   Future<Map<String, dynamic>> getAccountStats(AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final List<Map<String, dynamic>> result = await db.rawQuery('''
//         SELECT 
//           COUNT(*) as total_count,
//           COUNT(CASE WHEN ${columns['deleted_at']!} IS NULL THEN 1 END) as active_count,
//           COUNT(CASE WHEN ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as debt_count,
//           COUNT(CASE WHEN ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as credit_count,
//           COUNT(CASE WHEN ${columns['balance']!} = 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as balanced_count,
//           COALESCE(SUM(CASE WHEN ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL THEN ${columns['balance']!} END), 0) as total_debt,
//           COALESCE(SUM(CASE WHEN ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL THEN ABS(${columns['balance']!}) END), 0) as total_credit
//         FROM $tableName
//       ''');

//       return result.first;
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع إحصائيات ${type.pluralName}', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة استرجاع الإحصائيات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           final List<Map<String, dynamic>> result = await db.rawQuery('''
//             SELECT 
//               COUNT(*) as total_count,
//               COUNT(CASE WHEN ${columns['deleted_at']!} IS NULL THEN 1 END) as active_count,
//               COUNT(CASE WHEN ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as debt_count,
//               COUNT(CASE WHEN ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as credit_count,
//               COUNT(CASE WHEN ${columns['balance']!} = 0 AND ${columns['deleted_at']!} IS NULL THEN 1 END) as balanced_count,
//               COALESCE(SUM(CASE WHEN ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL THEN ${columns['balance']!} END), 0) as total_debt,
//               COALESCE(SUM(CASE WHEN ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL THEN ABS(${columns['balance']!}) END), 0) as total_credit
//             FROM $tableName
//           ''');

//           return result.first;
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات واسترجاع الإحصائيات', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// الحصول على الحسابات غير المتزامنة
//   Future<List<Account>> getUnsyncedAccounts(AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: '${columns['is_synced']!} = 0',
//         orderBy: columns['updated_at']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName} غير المتزامنة', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة استرجاع البيانات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           final List<Map<String, dynamic>> maps = await db.query(
//             tableName,
//             where: '${columns['is_synced']!} = 0',
//             orderBy: columns['updated_at']!,
//           );

//           return maps.map((map) => _mapToAccount(map, type)).toList();
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات واسترجاع البيانات', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }

//   /// تحديث حالة المزامنة
//   Future<void> markAsSynced(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       await db.update(
//         tableName,
//         {columns['is_synced']!: 1},
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       AppUtils.logInfo('تم تحديث حالة المزامنة لـ ${type.displayName} - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في تحديث حالة المزامنة', e);
      
//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات وإعادة المحاولة
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات وإعادة تحديث حالة المزامنة...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           final tableName = _getTableName(type);
//           final columns = _getColumnNames(type);

//           await db.update(
//             tableName,
//             {columns['is_synced']!: 1},
//             where: '${columns['id']!} = ?',
//             whereArgs: [id],
//           );

//           AppUtils.logInfo('تم تحديث حالة المزامنة لـ ${type.displayName} بعد إعادة تعيين قاعدة البيانات - ID: $id');
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات وتحديث حالة المزامنة', resetError);
//           throw Exception(ErrorHandler.handleDatabaseError(resetError));
//         }
//       } else {
//         throw Exception(ErrorHandler.handleDatabaseError(e));
//       }
//     }
//   }
// }
// /*class AccountDao {
//   final DatabaseHelper _databaseHelper;

//   AccountDao(this._databaseHelper);

//   /// الحصول على قاعدة البيانات
//   Future<Database> get database async {
//     try {
//       final db = await _databaseHelper.database;
//       AppUtils.logInfo('Database access successful in AccountDao');
//       return db;
//     } catch (e) {
//       AppUtils.logError('Database access failed in AccountDao', e);

//       // في حالة خطأ "read-only"، حاول إعادة تعيين قاعدة البيانات
//       if (e.toString().contains('read-only')) {
//         AppUtils.logInfo('محاولة إعادة تعيين قاعدة البيانات...');
//         try {
//           await _databaseHelper.resetDatabase();
//           final db = await _databaseHelper.database;
//           AppUtils.logInfo('تم إعادة تعيين قاعدة البيانات بنجاح');
//           return db;
//         } catch (resetError) {
//           AppUtils.logError('فشل في إعادة تعيين قاعدة البيانات', resetError);
//         }
//       }

//       rethrow;
//     }
//   }

//   /// الحصول على اسم الجدول حسب نوع الحساب
//   String _getTableName(AccountType type) {
//     switch (type) {
//       case AccountType.customer:
//         return DatabaseConstants.tableCustomers;
//       case AccountType.supplier:
//         return DatabaseConstants.tableSuppliers;
//     }
//   }

//   /// الحصول على أسماء الأعمدة حسب نوع الحساب
//   Map<String, String> _getColumnNames(AccountType type) {
//     switch (type) {
//       case AccountType.customer:
//         return {
//           'id': DatabaseConstants.columnCustomerId,
//           'name': DatabaseConstants.columnCustomerName,
//           'phone': DatabaseConstants.columnCustomerPhone,
//           'email': DatabaseConstants.columnCustomerEmail,
//           'address': DatabaseConstants.columnCustomerAddress,
//           'balance': DatabaseConstants.columnCustomerBalance,
//           'notes': DatabaseConstants.columnCustomerNotes,
//           'created_at': DatabaseConstants.columnCustomerCreatedAt,
//           'updated_at': DatabaseConstants.columnCustomerUpdatedAt,
//           'deleted_at': DatabaseConstants.columnCustomerDeletedAt,
//           'is_synced': DatabaseConstants.columnCustomerIsSynced,
//         };
//       case AccountType.supplier:
//         return {
//           'id': DatabaseConstants.columnSupplierId,
//           'name': DatabaseConstants.columnSupplierName,
//           'phone': DatabaseConstants.columnSupplierPhone,
//           'email': DatabaseConstants.columnSupplierEmail,
//           'address': DatabaseConstants.columnSupplierAddress,
//           'balance': DatabaseConstants.columnSupplierBalance,
//           'notes': DatabaseConstants.columnSupplierNotes,
//           'created_at': DatabaseConstants.columnSupplierCreatedAt,
//           'updated_at': DatabaseConstants.columnSupplierUpdatedAt,
//           'deleted_at': DatabaseConstants.columnSupplierDeletedAt,
//           'is_synced': DatabaseConstants.columnSupplierIsSynced,
//         };
//     }
//   }

//   /// تحويل Account إلى Map للحفظ في قاعدة البيانات
//   Map<String, dynamic> _accountToMap(Account account) {
//     final columns = _getColumnNames(account.type);
//     return {
//       columns['id']!: account.id,
//       columns['name']!: account.name,
//       columns['phone']!: account.phone,
//       columns['email']!: account.email,
//       columns['address']!: account.address,
//       columns['balance']!: account.balance,
//       columns['notes']!:
//           account.notes, // ملاحظة: notes غير موجود في الجداول الحالية
//       columns['created_at']!: account.createdAt?.toIso8601String(),
//       columns['updated_at']!: account.updatedAt?.toIso8601String(),
//       columns['deleted_at']!: account.deletedAt?.toIso8601String(),
//       columns['is_synced']!: account.isSynced ? 1 : 0,
//     };
//   }

//   /// تحويل Map من قاعدة البيانات إلى Account
//   Account _mapToAccount(Map<String, dynamic> map, AccountType type) {
//     // إضافة معلومة نوع الجدول للمساعدة في التحويل
//     map['_table_name'] = _getTableName(type);
//     return Account.fromMap(map);
//   }

//   // ------------------------------------------------------------------
//   // العمليات الأساسية - Basic Operations
//   // ------------------------------------------------------------------

//   /// إنشاء حساب جديد
//   Future<String> insert(Account account) async {
//     try {
//       AppUtils.logInfo(
//           'بدء إدراج ${account.type.displayName}: ${account.name}');

//       final db = await database; // استخدام getter المحسن
//       final tableName = _getTableName(account.type);
//       final map = _accountToMap(account);

//       // إضافة الطوابع الزمنية
//       final now = DateTime.now().toIso8601String();
//       map[_getColumnNames(account.type)['created_at']!] = now;
//       map[_getColumnNames(account.type)['updated_at']!] = now;
//       map[_getColumnNames(account.type)['is_synced']!] = 0;

//       // توليد ID إذا لم يكن موجود
//       if (map[_getColumnNames(account.type)['id']!] == null ||
//           map[_getColumnNames(account.type)['id']!].toString().isEmpty) {
//         map[_getColumnNames(account.type)['id']!] = AppUtils.generateId();
//       }

//       AppUtils.logInfo('إدراج في الجدول: $tableName');
//       AppUtils.logInfo('البيانات: ${map.keys.join(', ')}');

//       await db.insert(tableName, map);

//       AppUtils.logInfo(
//           'تم إدراج ${account.type.displayName} بنجاح - ID: ${map[_getColumnNames(account.type)['id']!]}');
//       return map[_getColumnNames(account.type)['id']!];
//     } catch (e) {
//       AppUtils.logError('خطأ في إدراج ${account.type.displayName}', e);
//       rethrow; // إعادة رمي الخطأ الأصلي بدون تعديل
//     }
//   }

//   /// تحديث حساب موجود
//   Future<void> update(Account account) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(account.type);
//       final columns = _getColumnNames(account.type);
//       final map = _accountToMap(account);

//       // تحديث الطابع الزمني
//       map[columns['updated_at']!] = DateTime.now().toIso8601String();
//       map[columns['is_synced']!] = 0;

//       final rowsAffected = await db.update(
//         tableName,
//         map,
//         where: '${columns['id']!} = ?',
//         whereArgs: [account.id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception(
//             'لم يتم العثور على ${account.type.displayName} للتحديث');
//       }

//       AppUtils.logInfo(
//           'تم تحديث ${account.type.displayName} بنجاح - ID: ${account.id}');
//     } catch (e) {
//       AppUtils.logError('خطأ في تحديث ${account.type.displayName}', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// حذف حساب (حذف ناعم)
//   Future<void> delete(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final rowsAffected = await db.update(
//         tableName,
//         {
//           columns['deleted_at']!: DateTime.now().toIso8601String(),
//           columns['updated_at']!: DateTime.now().toIso8601String(),
//           columns['is_synced']!: 0,
//         },
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception('لم يتم العثور على ${type.displayName} للحذف');
//       }

//       AppUtils.logInfo('تم حذف ${type.displayName} بنجاح - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في حذف ${type.displayName}', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// حذف حساب نهائ
//   Future<void> deleteForever(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final rowsAffected = await db.delete(
//         tableName,
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       if (rowsAffected == 0) {
//         throw Exception('لم يتم العثور على ${type.displayName} للحذف النهائي');
//       }

//       AppUtils.logInfo('تم حذف ${type.displayName} نهائـ - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في الحذف النهائي لـ ${type.displayName}', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// البحث عن حساب بالمعرف
//   Future<Account?> findById(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//         limit: 1,
//       );

//       if (maps.isEmpty) return null;

//       return _mapToAccount(maps.first, type);
//     } catch (e) {
//       AppUtils.logError('خطأ في البحث عن ${type.displayName}', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// الحصول على جميع الحسابات من نوع معين
//   Future<List<Account>> findAll(AccountType type,
//       {bool includeDeleted = false}) async {
//     try {
//       AppUtils.logInfo('بدء استرجاع ${type.pluralName}');

//       final db = await database; // استخدام getter المحسن
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String? where;
//       if (!includeDeleted) {
//         where = '${columns['deleted_at']!} IS NULL';
//       }

//       AppUtils.logInfo('استعلام من الجدول: $tableName');
//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         orderBy: columns['name']!,
//       );

//       AppUtils.logInfo('تم استرجاع ${maps.length} من ${type.pluralName}');
//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName}', e);
//       rethrow; // إعادة رمي الخطأ الأصلي بدون تعديل
//     }
//   }

//   /// البحث في الحسابات
//   Future<List<Account>> search(
//     AccountType type,
//     String query, {
//     bool includeDeleted = false,
//   }) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String where =
//           '(${columns['name']!} LIKE ? OR ${columns['phone']!} LIKE ? OR ${columns['email']!} LIKE ?)';
//       List<dynamic> whereArgs = ['%$query%', '%$query%', '%$query%'];
//       if (!includeDeleted) {
//         where += ' AND ${columns['deleted_at']!} IS NULL';
//       }

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         whereArgs: whereArgs,
//         orderBy: columns['name']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في البحث في ${type.pluralName}', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// الحصول على الحسابات حسب حالة الرصيد
//   Future<List<Account>> findByBalanceStatus(
//     AccountType type,
//     BalanceStatus status, {
//     bool includeDeleted = false,
//   }) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       String balanceCondition;
//       switch (status) {
//         case BalanceStatus.debt:
//           balanceCondition = '${columns['balance']!} > 0';
//           break;
//         case BalanceStatus.credit:
//           balanceCondition = '${columns['balance']!} < 0';
//           break;
//         case BalanceStatus.balanced:
//           balanceCondition = '${columns['balance']!} = 0';
//           break;
//       }

//       String where = balanceCondition;
//       if (!includeDeleted) {
//         where += ' AND ${columns['deleted_at']!} IS NULL';
//       }

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: where,
//         orderBy: columns['name']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName} حسب حالة الرصيد', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// الحصول على إحصائيات الحسابات
//   Future<Map<String, dynamic>> getAccountStats(AccountType type) async {
//     try {
//       final db = await database; // استخدام getter المحسن
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       // حساب الإحصائيات بطريقة مبسطة
//       final stats = <String, dynamic>{};

//       // العدد الإجمالي
//       final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM $tableName');
//       stats['total_count'] = totalResult.first['count'] ?? 0;

//       // العدد النشط (غير محذوف)
//       final activeResult = await db.rawQuery(
//         'SELECT COUNT(*) as count FROM $tableName WHERE ${columns['deleted_at']!} IS NULL'
//       );
//       stats['active_count'] = activeResult.first['count'] ?? 0;

//       // الحسابات المدينة (رصيد موجب)
//       final debtResult = await db.rawQuery(
//           'SELECT COUNT(*) as count FROM $tableName WHERE ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL');
//       stats['debt_count'] = debtResult.first['count'] ?? 0;

//       // الحسابات الدائنة (رصيد سالب)
//       final creditResult = await db.rawQuery(
//           'SELECT COUNT(*) as count FROM $tableName WHERE ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL');
//       stats['credit_count'] = creditResult.first['count'] ?? 0;

//       // الحسابات المتوازنة (رصيد صفر)
//       final balancedResult = await db.rawQuery(
//           'SELECT COUNT(*) as count FROM $tableName WHERE ${columns['balance']!} = 0 AND ${columns['deleted_at']!} IS NULL');
//       stats['balanced_count'] = balancedResult.first['count'] ?? 0;

//       // إجمالي الديون
//       final totalDebtResult = await db.rawQuery(
//           'SELECT SUM(${columns['balance']!}) as total FROM $tableName WHERE ${columns['balance']!} > 0 AND ${columns['deleted_at']!} IS NULL');
//       stats['total_debt'] = totalDebtResult.first['total'] ?? 0.0;

//       // إجمالي الائتمان
//       final totalCreditResult = await db.rawQuery(
//           'SELECT SUM(ABS(${columns['balance']!})) as total FROM $tableName WHERE ${columns['balance']!} < 0 AND ${columns['deleted_at']!} IS NULL');
//       stats['total_credit'] = totalCreditResult.first['total'] ?? 0.0;

//       AppUtils.logInfo('تم حساب إحصائيات ${type.pluralName} بنجاح');
//       return stats;
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع إحصائيات ${type.pluralName}', e);
//       // إرجاع إحصائيات فارغة بدلاً من رمي خطأ
//       return {
//         'total_count': 0,
//         'active_count': 0,
//         'debt_count': 0,
//         'credit_count': 0,
//         'balanced_count': 0,
//         'total_debt': 0.0,
//         'total_credit': 0.0,
//       };
//     }
//   }

//   /// الحصول على الحسابات غير المتزامنة
//   Future<List<Account>> getUnsyncedAccounts(AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       final List<Map<String, dynamic>> maps = await db.query(
//         tableName,
//         where: '${columns['is_synced']!} = 0',
//         orderBy: columns['updated_at']!,
//       );

//       return maps.map((map) => _mapToAccount(map, type)).toList();
//     } catch (e) {
//       AppUtils.logError('خطأ في استرجاع ${type.pluralName} غير المتزامنة', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }

//   /// تحديث حالة المزامنة
//   Future<void> markAsSynced(String id, AccountType type) async {
//     try {
//       final db = await _databaseHelper.database;
//       final tableName = _getTableName(type);
//       final columns = _getColumnNames(type);

//       await db.update(
//         tableName,
//         {columns['is_synced']!: 1},
//         where: '${columns['id']!} = ?',
//         whereArgs: [id],
//       );

//       AppUtils.logInfo(
//           'تم تحديث حالة المزامنة لـ ${type.displayName} - ID: $id');
//     } catch (e) {
//       AppUtils.logError('خطأ في تحديث حالة المزامنة', e);
//       throw Exception(ErrorHandler.handleDatabaseError(e));
//     }
//   }
// }
// */

