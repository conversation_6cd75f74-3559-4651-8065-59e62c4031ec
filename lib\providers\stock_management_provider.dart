import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/data/models/stock_transaction.dart';
import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/core/utils/app_utils.dart';
import 'package:tijari_tech/core/exceptions/app_exceptions.dart';
import 'package:tijari_tech/providers/enhanced_product_provider.dart';
import 'package:tijari_tech/providers/stock_provider.dart' as stock_provider;
import 'package:uuid/uuid.dart';

/// مزود إدارة المخزون
class StockManagementNotifier extends StateNotifier<StockManagementState> {
  StockManagementNotifier(this._ref) : super(const StockManagementState());

  final Ref _ref;
  final _uuid = const Uuid();

  /// تعديل مخزون المنتج
  /// يدعم الآن ربط المخزون بالمخازن
  Future<void> adjustStock({
    required String productId,
    required String operationType, // 'add', 'subtract', 'set'
    required double quantity,
    String? reason,
    String? notes,
    String transactionType = 'adjustment',
    String? warehouseId, // إضافة دعم المخازن
    String? userId, // إضافة معرف المستخدم
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // التحقق من صحة البيانات المدخلة
      await _validateStockAdjustment(
        productId: productId,
        operationType: operationType,
        quantity: quantity,
        reason: reason,
        warehouseId: warehouseId,
        userId: userId,
      );

      // الحصول على المنتج الحالي
      final productNotifier =
          _ref.read(enhancedProductManagementProvider.notifier);
      final currentProducts =
          _ref.read(enhancedProductManagementProvider).products;
      final product = currentProducts.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('المنتج غير موجود'),
      );

      final previousStock = product.currentStock ?? 0;
      AppUtils.logInfo('Previous stock: $previousStock');
      double newStock;

      // حساب المخزون الجديد
      switch (operationType) {
        case 'add':
          newStock = previousStock + quantity;
          break;
        case 'subtract':
          newStock = previousStock - quantity;
          if (newStock < 0) {
            throw Exception('لا يمكن أن تكون الكمية أقل من الصفر');
          }
          break;
        case 'set':
          newStock = quantity;
          break;
        default:
          throw Exception('نوع العملية غير صحيح');
      }

      // إنشاء معاملة المخزون
      final transaction = StockTransaction(
        id: _uuid.v4(),
        productId: productId,
        type: transactionType,
        operation: operationType,
        quantity: quantity,
        previousStock: previousStock,
        newStock: newStock,
        reason: reason!,
        notes: notes,
        createdAt: DateTime.now(),
      );

      // تحديث المخزون في النظام المتقدم (Stock System)
      if (warehouseId != null && userId != null) {
        // استخدام نظام المخزون المتقدم مع المخازن
        final stockRepository =
            _ref.read(stock_provider.stockRepositoryProvider);

        // حساب الكمية الصحيحة للحركة بناءً على نوع العملية
        double movementQuantity;

        switch (operationType) {
          case 'add':
            movementQuantity = quantity; // إضافة موجبة
            break;
          case 'subtract':
            movementQuantity = -quantity; // طرح سالب
            break;
          case 'set':
            // للتعيين المباشر، نحسب الفرق
            movementQuantity = newStock - previousStock;
            break;
          default:
            throw Exception('نوع العملية غير صحيح');
        }

        await stockRepository.updateStockWithMovement(
          productId: productId,
          warehouseId: warehouseId,
          movementType: 'adjustment',
          quantity: movementQuantity,
          unitCost: product.costPrice,
          referenceType: 'stock_adjustment',
          referenceId: transaction.id,
          notes: notes,
          reason: reason,
          createdBy: userId,
        );
      }

      // تحديث المنتج بالمخزون الجديد (للعرض السريع)
      await productNotifier.updateProduct(
        id: product.id,
        currentStock: newStock,
      );

      // إضافة المعاملة إلى السجل
      final updatedTransactions = [...state.transactions, transaction];
      state = state.copyWith(
        transactions: updatedTransactions,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// الحصول على سجل معاملات منتج معين
  List<StockTransaction> getProductTransactions(String productId) {
    return state.transactions
        .where((transaction) => transaction.productId == productId)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// مسح الأخطاء
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// تحديث حالة التحميل
  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  /// التحقق من صحة بيانات تعديل المخزون
  Future<void> _validateStockAdjustment({
    required String productId,
    required String operationType,
    required double quantity,
    String? reason,
    String? warehouseId,
    String? userId,
  }) async {
    // التحقق من صحة معرف المنتج
    if (productId.trim().isEmpty) {
      throw ValidationException('معرف المنتج مطلوب');
    }

    // التحقق من صحة نوع العملية
    if (!['add', 'subtract', 'set'].contains(operationType)) {
      throw ValidationException('نوع العملية غير صحيح');
    }

    // التحقق من صحة الكمية
    if (quantity < 0) {
      throw ValidationException('الكمية لا يمكن أن تكون سالبة');
    }

    if (quantity == 0 && operationType != 'set') {
      throw ValidationException('الكمية يجب أن تكون أكبر من الصفر');
    }

    // التحقق من وجود السبب للعمليات الحساسة
    if ((operationType == 'subtract' || operationType == 'set') &&
        (reason == null || reason.trim().isEmpty)) {
      throw ValidationException('السبب مطلوب لعمليات الطرح والتعديل المباشر');
    }

    // التحقق من وجود المنتج
    final currentProducts =
        _ref.read(enhancedProductManagementProvider).products;
    final product = currentProducts.where((p) => p.id == productId).firstOrNull;

    if (product == null) {
      throw ValidationException('المنتج غير موجود');
    }

    // التحقق من حالة المنتج
    if (!product.isActive) {
      throw ValidationException('لا يمكن تعديل مخزون منتج غير نشط');
    }

    // التحقق من المخزون السالب للمنتجات التي لا تسمح بذلك
    if (operationType == 'subtract') {
      final currentStock = product.currentStock ?? 0;
      final newStock = currentStock - quantity;

      if (newStock < 0 && !product.allowNegativeStock) {
        throw ValidationException('لا يمكن أن يصبح المخزون سالباً لهذا المنتج. '
            'المخزون الحالي: $currentStock، الكمية المطلوبة: $quantity');
      }
    }
  }
}

/// مزود إدارة المخزون
final stockManagementProvider =
    StateNotifierProvider<StockManagementNotifier, StockManagementState>(
  (ref) => StockManagementNotifier(ref),
);

/// مزود للحصول على معاملات منتج معين
final productStockTransactionsProvider =
    Provider.family<List<StockTransaction>, String>((ref, productId) {
  final stockState = ref.watch(stockManagementProvider);
  return stockState.transactions
      .where((transaction) => transaction.productId == productId)
      .toList()
    ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
});

/// مزود لحساب إجمالي الحركات
final stockSummaryProvider = Provider<Map<String, dynamic>>((ref) {
  final stockState = ref.watch(stockManagementProvider);
  final transactions = stockState.transactions;

  int totalAdjustments = 0;
  int totalPurchases = 0;
  int totalSales = 0;
  int totalReturns = 0;

  for (final transaction in transactions) {
    switch (transaction.type) {
      case 'adjustment':
        totalAdjustments++;
        break;
      case 'purchase':
        totalPurchases++;
        break;
      case 'sale':
        totalSales++;
        break;
      case 'return':
        totalReturns++;
        break;
    }
  }

  return {
    'totalTransactions': transactions.length,
    'totalAdjustments': totalAdjustments,
    'totalPurchases': totalPurchases,
    'totalSales': totalSales,
    'totalReturns': totalReturns,
  };
});

/// مزود للتحقق من حالة المخزون المنخفض
final lowStockProductsProvider = Provider<List<Product>>((ref) {
  final products = ref.watch(enhancedProductManagementProvider).products;

  return products.where((product) {
    if (!product.trackStock) return false;
    final currentStock = product.currentStock ?? 0;
    return currentStock <= product.minStock;
  }).toList();
});

/// مزود للتحقق من المنتجات النافدة
final outOfStockProductsProvider = Provider<List<Product>>((ref) {
  final products = ref.watch(enhancedProductManagementProvider).products;

  return products.where((product) {
    if (!product.trackStock) return false;
    final currentStock = product.currentStock ?? 0;
    return currentStock <= 0;
  }).toList();
});
