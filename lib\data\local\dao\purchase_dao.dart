import 'package:sqflite/sqflite.dart';
import 'base_dao.dart';
import '../../models/purchase.dart';
import '../../models/purchase_item.dart';
import '../../../core/utils/app_utils.dart';
import '../../../core/constants/database_constants.dart';

class PurchaseDao extends BaseDao<Purchase> {
  @override
  String get tableName => 'purchases';

  @override
  Purchase fromMap(Map<String, dynamic> map) => Purchase.fromMap(map);

  @override
  Map<String, dynamic> toMap(Purchase entity) => entity.toMap();

  // Create purchase with items
  Future<String> createPurchaseWithItems(
      Purchase purchase, List<PurchaseItem> items) async {
    try {
      final db = await database;
      String purchaseId = '';

      await db.transaction((txn) async {
        // Insert purchase
        final purchaseMap = toMap(purchase);
        purchaseMap['created_at'] = DateTime.now().toIso8601String();
        purchaseMap['updated_at'] = purchaseMap['created_at'];
        purchaseMap['is_synced'] = 0;

        if (purchaseMap['id'] == null || purchaseMap['id'].toString().isEmpty) {
          purchaseMap['id'] = AppUtils.generateId();
        }
        purchaseId = purchaseMap['id'];

        await txn.insert('purchases', purchaseMap);

        // Insert purchase items
        for (final item in items) {
          final itemMap = item.toMap();
          itemMap['purchase_id'] = purchaseId;
          itemMap['created_at'] = purchaseMap['created_at'];
          itemMap['updated_at'] = purchaseMap['created_at'];
          itemMap['is_synced'] = 0;

          if (itemMap['id'] == null || itemMap['id'].toString().isEmpty) {
            itemMap['id'] = AppUtils.generateId();
          }

          await txn.insert('purchase_items', itemMap);

          // Update stock
          await _updateStock(txn, item.productId, item.qty, 'in');

          // Update product cost price
          await _updateProductCostPrice(txn, item.productId, item.unitPrice);
        }

        // Create stock movements
        await _createStockMovements(txn, purchaseId, items);

        // Update supplier balance if credit purchase
        if (purchase.supplierId != null && purchase.dueAmount > 0) {
          await _updateSupplierBalance(
              txn, purchase.supplierId!, purchase.dueAmount);
        }

        // Create transaction record
        await _createTransaction(txn, purchaseId, purchase);
      });

      AppUtils.logInfo(
          'Created purchase with ${items.length} items, ID: $purchaseId');
      return purchaseId;
    } catch (e) {
      AppUtils.logError('Error creating purchase with items', e);
      throw Exception('Failed to create purchase: ${e.toString()}');
    }
  }

  // Get purchase with items
  Future<Map<String, dynamic>?> getPurchaseWithItems(String purchaseId) async {
    final sql = '''
      SELECT 
        p.*,
        s.name as supplier_name,
        b.name as branch_name
      FROM purchases p
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN branches b ON p.branch_id = b.id
      WHERE p.id = ? AND p.deleted_at IS NULL
    ''';

    final purchaseResult = await rawQuery(sql, [purchaseId]);
    if (purchaseResult.isEmpty) return null;

    final purchase = purchaseResult.first;

    // Get purchase items
    final itemsSql = '''
      SELECT 
        pi.*,
        p.name_ar as product_name,
        p.barcode,
        u.name as unit_name
      FROM purchase_items pi
      JOIN products p ON pi.product_id = p.id
      LEFT JOIN units u ON pi.unit_id = u.id
      WHERE pi.purchase_id = ?
      ORDER BY pi.created_at
    ''';

    final items = await rawQuery(itemsSql, [purchaseId]);

    return {
      'purchase': purchase,
      'items': items,
    };
  }

  // Get purchases by date range
  Future<List<Map<String, dynamic>>> getPurchasesByDateRange(
      DateTime startDate, DateTime endDate) async {
    final sql = '''
      SELECT 
        p.*,
        s.name as supplier_name,
        COUNT(pi.id) as item_count
      FROM purchases p
      LEFT JOIN suppliers s ON p.supplier_id = s.id
      LEFT JOIN purchase_items pi ON p.id = pi.purchase_id
      WHERE p.purchase_date >= ? AND p.purchase_date <= ? AND p.deleted_at IS NULL
      GROUP BY p.id
      ORDER BY p.purchase_date DESC, p.created_at DESC
    ''';

    return await rawQuery(sql, [
      startDate.toIso8601String(),
      endDate.toIso8601String(),
    ]);
  }

  // Get purchases by supplier
  Future<List<Map<String, dynamic>>> getPurchasesBySupplier(
      String supplierId) async {
    final sql = '''
      SELECT 
        p.*,
        COUNT(pi.id) as item_count
      FROM purchases p
      LEFT JOIN purchase_items pi ON p.id = pi.purchase_id
      WHERE p.supplier_id = ? AND p.deleted_at IS NULL
      GROUP BY p.id
      ORDER BY p.purchase_date DESC
    ''';

    return await rawQuery(sql, [supplierId]);
  }

  // Get daily purchases summary
  Future<List<Map<String, dynamic>>> getDailyPurchasesSummary(
      {int days = 30}) async {
    final startDate = DateTime.now().subtract(Duration(days: days));

    final sql = '''
      SELECT 
        DATE(purchase_date) as date,
        COUNT(*) as total_purchases,
        SUM(total_amount) as total_amount,
        SUM(paid_amount) as total_paid,
        SUM(due_amount) as total_due,
        AVG(total_amount) as avg_amount
      FROM purchases
      WHERE purchase_date >= ? AND deleted_at IS NULL
      GROUP BY DATE(purchase_date)
      ORDER BY date DESC
    ''';

    return await rawQuery(sql, [startDate.toIso8601String()]);
  }

  // Get top suppliers by purchases
  Future<List<Map<String, dynamic>>> getTopSuppliersByPurchases(
      {int limit = 10}) async {
    final sql = '''
      SELECT 
        s.id,
        s.name,
        COUNT(p.id) as total_purchases,
        SUM(p.total_amount) as total_amount,
        SUM(p.paid_amount) as total_paid,
        SUM(p.due_amount) as total_due
      FROM suppliers s
      JOIN purchases p ON s.id = p.supplier_id
      WHERE p.deleted_at IS NULL AND s.deleted_at IS NULL
      GROUP BY s.id, s.name
      ORDER BY total_amount DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  // Get pending payments to suppliers
  Future<List<Map<String, dynamic>>> getPendingPayments() async {
    final sql = '''
      SELECT 
        p.*,
        s.name as supplier_name,
        s.phone as supplier_phone
      FROM purchases p
      JOIN suppliers s ON p.supplier_id = s.id
      WHERE p.due_amount > 0 AND p.deleted_at IS NULL AND s.deleted_at IS NULL
      ORDER BY p.purchase_date ASC
    ''';

    return await rawQuery(sql);
  }

  // Update purchase payment
  Future<bool> updatePayment(String purchaseId, double paidAmount) async {
    try {
      final db = await database;

      await db.transaction((txn) async {
        // Get current purchase
        final purchaseResult = await txn.query(
          'purchases',
          where: 'id = ? AND deleted_at IS NULL',
          whereArgs: [purchaseId],
          limit: 1,
        );

        if (purchaseResult.isEmpty) {
          throw Exception('Purchase not found');
        }

        final purchase = purchaseResult.first;
        final currentPaid = (purchase['paid_amount'] as num).toDouble();
        final totalAmount = (purchase['total_amount'] as num).toDouble();
        final newPaidAmount = currentPaid + paidAmount;
        final newDueAmount = totalAmount - newPaidAmount;

        if (newPaidAmount > totalAmount) {
          throw Exception('Payment amount exceeds total amount');
        }

        // Update purchase
        await txn.update(
          'purchases',
          {
            'paid_amount': newPaidAmount,
            'due_amount': newDueAmount,
            'updated_at': DateTime.now().toIso8601String(),
            'is_synced': 0,
          },
          where: 'id = ?',
          whereArgs: [purchaseId],
        );

        // Update supplier balance
        final supplierId = purchase['supplier_id'] as String?;
        if (supplierId != null) {
          await _updateSupplierBalance(txn, supplierId, -paidAmount);
        }

        // Create payment transaction
        await txn.insert('transactions', {
          'id': AppUtils.generateId(),
          'type': 'payment',
          'amount': paidAmount,
          'related_to': 'purchase',
          'related_id': purchaseId,
          'supplier_id': supplierId,
          'transaction_date': DateTime.now().toIso8601String(),
          'notes': 'Payment for purchase #${purchase['invoice_no']}',
          'created_at': DateTime.now().toIso8601String(),
          'is_synced': 0,
        });
      });

      AppUtils.logInfo(
          'Updated payment for purchase: $purchaseId, amount: $paidAmount');
      return true;
    } catch (e) {
      AppUtils.logError('Error updating purchase payment', e);
      return false;
    }
  }

  // Cancel purchase
  Future<bool> cancelPurchase(String purchaseId, String reason) async {
    try {
      final db = await database;

      await db.transaction((txn) async {
        // Get purchase with items
        final purchaseResult = await txn.query(
          'purchases',
          where: 'id = ? AND deleted_at IS NULL',
          whereArgs: [purchaseId],
          limit: 1,
        );

        if (purchaseResult.isEmpty) {
          throw Exception('Purchase not found');
        }

        final purchase = purchaseResult.first;

        // Get purchase items
        final items = await txn.query(
          'purchase_items',
          where: 'purchase_id = ?',
          whereArgs: [purchaseId],
        );

        // Reduce stock for each item
        for (final item in items) {
          await _updateStock(txn, item['product_id'] as String,
              (item['qty'] as num).toDouble(), 'out');
        }

        // Update supplier balance if there was due amount
        final supplierId = purchase['supplier_id'] as String?;
        final dueAmount = (purchase['due_amount'] as num).toDouble();
        if (supplierId != null && dueAmount > 0) {
          await _updateSupplierBalance(txn, supplierId, -dueAmount);
        }

        // Soft delete purchase and items
        final now = DateTime.now().toIso8601String();

        await txn.update(
          'purchases',
          {
            'deleted_at': now,
            'updated_at': now,
            'notes': '${purchase['notes'] ?? ''}\nCancelled: $reason',
            'is_synced': 0,
          },
          where: 'id = ?',
          whereArgs: [purchaseId],
        );

        await txn.update(
          'purchase_items',
          {
            'deleted_at': now,
            'updated_at': now,
            'is_synced': 0,
          },
          where: 'purchase_id = ?',
          whereArgs: [purchaseId],
        );

        // Create cancellation transaction
        await txn.insert('transactions', {
          'id': AppUtils.generateId(),
          'type': 'refund',
          'amount': purchase['paid_amount'],
          'related_to': 'purchase',
          'related_id': purchaseId,
          'supplier_id': supplierId,
          'transaction_date': now,
          'notes': 'Purchase cancellation: $reason',
          'created_at': now,
          'is_synced': 0,
        });
      });

      AppUtils.logInfo('Cancelled purchase: $purchaseId, reason: $reason');
      return true;
    } catch (e) {
      AppUtils.logError('Error cancelling purchase', e);
      return false;
    }
  }

  // Helper methods
  Future<void> _updateStock(
      Transaction txn, String productId, double qty, String type) async {
    final stockResult = await txn.query(
      'stocks',
      where: 'product_id = ?',
      whereArgs: [productId],
      limit: 1,
    );

    if (stockResult.isNotEmpty) {
      final currentQty = (stockResult.first['quantity'] as num).toDouble();
      final newQty = type == 'in' ? currentQty + qty : currentQty - qty;

      await txn.update(
        'stocks',
        {
          'quantity': newQty,
          'updated_at': DateTime.now().toIso8601String(),
          'is_synced': 0,
        },
        where: 'product_id = ?',
        whereArgs: [productId],
      );
    } else if (type == 'in') {
      // Create new stock record
      await txn.insert('stocks', {
        'id': AppUtils.generateId(),
        'product_id': productId,
        'quantity': qty,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_synced': 0,
      });
    }
  }

  Future<void> _updateProductCostPrice(
      Transaction txn, String productId, double costPrice) async {
    await txn.update(
      'products',
      {
        'cost_price': costPrice,
        'updated_at': DateTime.now().toIso8601String(),
        'is_synced': 0,
      },
      where: 'id = ?',
      whereArgs: [productId],
    );
  }

  Future<String> _getDefaultWarehouseId(Transaction txn) async {
    try {
      // البحث عن المخزن الافتراضي أولاً
      final defaultWarehouses = await txn.query(
        DatabaseConstants.tableWarehouses,
        where:
            'is_default = ? AND ${DatabaseConstants.columnWarehouseIsActive} = ? AND ${DatabaseConstants.columnWarehouseDeletedAt} IS NULL',
        whereArgs: [1, 1],
        limit: 1,
      );

      if (defaultWarehouses.isNotEmpty) {
        return defaultWarehouses.first[DatabaseConstants.columnWarehouseId]
            as String;
      }

      // البحث عن أي مخزن نشط
      final activeWarehouses = await txn.query(
        DatabaseConstants.tableWarehouses,
        where:
            '${DatabaseConstants.columnWarehouseIsActive} = ? AND ${DatabaseConstants.columnWarehouseDeletedAt} IS NULL',
        whereArgs: [1],
        limit: 1,
        orderBy: '${DatabaseConstants.columnWarehouseCreatedAt} ASC',
      );

      if (activeWarehouses.isNotEmpty) {
        return activeWarehouses.first[DatabaseConstants.columnWarehouseId]
            as String;
      }

      // Create default warehouse if none exists
      final defaultWarehouseId = AppUtils.generateId();
      await txn.insert(DatabaseConstants.tableWarehouses, {
        DatabaseConstants.columnWarehouseId: defaultWarehouseId,
        DatabaseConstants.columnWarehouseName: 'المخزن الرئيسي',
        'description': 'المخزن الافتراضي للنظام',
        DatabaseConstants.columnWarehouseLocation: 'الموقع الرئيسي',
        DatabaseConstants.columnWarehouseIsActive: 1,
        'is_default': 1,
        'warehouse_type': 'main',
        DatabaseConstants.columnWarehouseCreatedAt:
            DateTime.now().toIso8601String(),
        DatabaseConstants.columnWarehouseUpdatedAt:
            DateTime.now().toIso8601String(),
        DatabaseConstants.columnWarehouseIsSynced: 0,
      });

      return defaultWarehouseId;
    } catch (e) {
      AppUtils.logError('Error getting default warehouse ID', e);
      // إنشاء مخزن افتراضي جديد في حالة الخطأ
      final fallbackWarehouseId = AppUtils.generateId();
      await txn.insert(DatabaseConstants.tableWarehouses, {
        DatabaseConstants.columnWarehouseId: fallbackWarehouseId,
        DatabaseConstants.columnWarehouseName: 'المخزن الاحتياطي',
        'description': 'مخزن احتياطي تم إنشاؤه تلقائياً',
        DatabaseConstants.columnWarehouseLocation: 'موقع افتراضي',
        DatabaseConstants.columnWarehouseIsActive: 1,
        'is_default': 1,
        'warehouse_type': 'main',
        DatabaseConstants.columnWarehouseCreatedAt:
            DateTime.now().toIso8601String(),
        DatabaseConstants.columnWarehouseUpdatedAt:
            DateTime.now().toIso8601String(),
        DatabaseConstants.columnWarehouseIsSynced: 0,
      });
      return fallbackWarehouseId;
    }
  }

  Future<void> _createStockMovements(
      Transaction txn, String purchaseId, List<PurchaseItem> items) async {
    try {
      // Get default warehouse ID
      final defaultWarehouseId = await _getDefaultWarehouseId(txn);

      for (final item in items) {
        // التحقق من وجود المنتج
        final productExists = await _verifyProductExists(txn, item.productId);
        if (!productExists) {
          AppUtils.logError('Product not found for stock movement',
              'Product ID: ${item.productId}');
          continue; // تخطي هذا العنصر إذا لم يوجد المنتج
        }

        // إنشاء حركة المخزون
        await txn.insert(DatabaseConstants.tableStockMovements, {
          DatabaseConstants.columnStockMovementId: AppUtils.generateId(),
          DatabaseConstants.columnStockMovementProductId: item.productId,
          DatabaseConstants.columnStockMovementWarehouseId: defaultWarehouseId,
          DatabaseConstants.columnStockMovementType: 'in',
          DatabaseConstants.columnStockMovementQuantity: item.qty,
          DatabaseConstants.columnStockMovementQty:
              item.qty, // استخدام الثابت الصحيح
          DatabaseConstants.columnStockMovementUnitCost: item.unitPrice,
          DatabaseConstants.columnStockMovementTotalValue:
              item.qty * item.unitPrice,
          DatabaseConstants.columnStockMovementUnitId:
              item.unitId ?? 'unit_piece',
          DatabaseConstants.columnStockMovementReferenceType: 'purchase',
          DatabaseConstants.columnStockMovementReferenceId: purchaseId,
          DatabaseConstants.columnStockMovementNotes: 'Purchase transaction',
          DatabaseConstants.columnStockMovementCreatedBy:
              'system', // إضافة الحقل المطلوب
          DatabaseConstants.columnStockMovementCreatedAt:
              DateTime.now().toIso8601String(),
          DatabaseConstants.columnStockMovementIsSynced: 0,
        });

        // تحديث كمية المخزون
        await _updateStockQuantity(
            txn, item.productId, defaultWarehouseId, item.qty);
      }
    } catch (e) {
      AppUtils.logError('Error creating stock movements for purchase', e);
      rethrow;
    }
  }

  /// التحقق من وجود المنتج
  Future<bool> _verifyProductExists(Transaction txn, String productId) async {
    try {
      final result = await txn.query(
        DatabaseConstants.tableProducts,
        where:
            '${DatabaseConstants.columnProductId} = ? AND ${DatabaseConstants.columnProductDeletedAt} IS NULL',
        whereArgs: [productId],
        limit: 1,
      );
      return result.isNotEmpty;
    } catch (e) {
      AppUtils.logError('Error verifying product exists', e);
      return false;
    }
  }

  /// تحديث كمية المخزون
  Future<void> _updateStockQuantity(Transaction txn, String productId,
      String warehouseId, double quantityChange) async {
    try {
      // البحث عن سجل المخزون الحالي
      final existingStock = await txn.query(
        DatabaseConstants.tableStocks,
        where:
            '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
        whereArgs: [productId, warehouseId],
        limit: 1,
      );

      if (existingStock.isNotEmpty) {
        // تحديث المخزون الموجود
        final currentQuantity =
            (existingStock.first[DatabaseConstants.columnStockQuantity] as num?)
                    ?.toDouble() ??
                0.0;
        final newQuantity = currentQuantity + quantityChange;

        await txn.update(
          DatabaseConstants.tableStocks,
          {
            DatabaseConstants.columnStockQuantity: newQuantity,
            'available_quantity': newQuantity, // تحديث الكمية المتاحة أيضاً
            DatabaseConstants.columnStockUpdatedAt:
                DateTime.now().toIso8601String(),
          },
          where:
              '${DatabaseConstants.columnStockProductId} = ? AND ${DatabaseConstants.columnStockWarehouseId} = ?',
          whereArgs: [productId, warehouseId],
        );
      } else {
        // إنشاء سجل مخزون جديد
        await txn.insert(DatabaseConstants.tableStocks, {
          DatabaseConstants.columnStockId: AppUtils.generateId(),
          DatabaseConstants.columnStockProductId: productId,
          DatabaseConstants.columnStockWarehouseId: warehouseId,
          DatabaseConstants.columnStockQuantity: quantityChange,
          'reserved_quantity': 0.0,
          'available_quantity': quantityChange,
          'min_stock': 0.0,
          'max_stock': 0.0,
          'reorder_point': 0.0,
          'average_cost': 0.0,
          DatabaseConstants.columnStockCreatedAt:
              DateTime.now().toIso8601String(),
          DatabaseConstants.columnStockUpdatedAt:
              DateTime.now().toIso8601String(),
          DatabaseConstants.columnStockIsSynced: 0,
        });
      }
    } catch (e) {
      AppUtils.logError('Error updating stock quantity', e);
      rethrow;
    }
  }

  Future<void> _updateSupplierBalance(
      Transaction txn, String supplierId, double amount) async {
    await txn.rawUpdate('''
      UPDATE suppliers 
      SET balance = balance + ?, 
          updated_at = ?, 
          is_synced = 0 
      WHERE id = ?
    ''', [amount, DateTime.now().toIso8601String(), supplierId]);
  }

  Future<void> _createTransaction(
      Transaction txn, String purchaseId, Purchase purchase) async {
    if (purchase.paidAmount > 0) {
      await txn.insert('transactions', {
        'id': AppUtils.generateId(),
        'type': 'payment',
        'amount': purchase.paidAmount,
        'related_to': 'purchase',
        'related_id': purchaseId,
        'supplier_id': purchase.supplierId,
        'transaction_date': purchase.purchaseDate.toIso8601String(),
        'notes': 'Purchase payment #${purchase.invoiceNo}',
        'created_at': DateTime.now().toIso8601String(),
        'is_synced': 0,
      });
    }
  }
}
