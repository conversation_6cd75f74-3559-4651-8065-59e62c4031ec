// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:tijari_tech/data/models/customer.dart';
// import 'package:tijari_tech/data/models/supplier.dart';
// import '../core/utils/app_utils.dart';
// import '../core/utils/error_handler.dart';
// import '../data/local/dao/customer_dao.dart';
// import '../data/local/dao/supplier_dao.dart';
// import '../data/local/database.dart';
// import '../data/repositories/customer_repository.dart';
// import '../data/repositories/supplier_repository.dart';

// /// حالة إدارة العملاء
// /// Customer Management State
// class CustomerState {
//   final List<Customer> customers;
//   final bool isLoading;
//   final String? error;
//   final String searchQuery;
//   final BalanceStatus? filterStatus;
//   final Map<String, dynamic>? statistics;
  
//   const CustomerState({
//     this.customers = const [],
//     this.isLoading = false,
//     this.error,
//     this.searchQuery = '',
//     this.filterStatus,
//     this.statistics,
//   });
  
//   CustomerState copyWith({
//     List<Customer>? customers,
//     bool? isLoading,
//     String? error,
//     String? searchQuery,
//     BalanceStatus? filterStatus,
//     Map<String, dynamic>? statistics,
//   }) {
//     return CustomerState(
//       customers: customers ?? this.customers,
//       isLoading: isLoading ?? this.isLoading,
//       error: error,
//       searchQuery: searchQuery ?? this.searchQuery,
//       filterStatus: filterStatus ?? this.filterStatus,
//       statistics: statistics ?? this.statistics,
//     );
//   }
  
//   /// الحصول على العملاء المفلترة
//   List<Customer> get filteredCustomers {
//     var filtered = customers;
    
//     // فلترة حسب البحث
//     if (searchQuery.isNotEmpty) {
//       filtered = filtered.where((customer) =>
//           customer.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
//           (customer.phone?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
//           (customer.email?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false))
//       .toList();
//     }
    
//     // فلترة حسب حالة الرصيد
//     if (filterStatus != null) {
//       filtered = filtered.where((customer) => customer.balanceStatus == filterStatus).toList();
//     }
    
//     return filtered;
//   }
  
//   /// عدد العملاء النشطين
//   int get activeCustomersCount => 
//       customers.where((c) => c.isActive).length;
  
//   /// عدد العملاء المدينين
//   int get debtCustomersCount => 
//       customers.where((c) => c.hasDebt).length;
  
//   /// عدد العملاء الدائنين
//   int get creditCustomersCount => 
//       customers.where((c) => c.hasCredit).length;
  
//   /// إجمالي المبالغ المدينة
//   double get totalDebt => customers
//       .where((c) => c.hasDebt)
//       .fold(0.0, (sum, customer) => sum + customer.balance);
  
//   /// إجمالي المبالغ الدائنة
//   double get totalCredit => customers
//       .where((c) => c.hasCredit)
//       .fold(0.0, (sum, customer) => sum + customer.absoluteBalance);
// }

// /// مزود إدارة العملاء
// /// Customer Management Provider
// class CustomerNotifier extends StateNotifier<CustomerState> {
//   final CustomerRepository _repository;
//   CustomerNotifier(this._repository) : super(const CustomerState());
  
//   // ------------------------------------------------------------------
//   // العمليات الأساسية - Basic Operations
//   // ------------------------------------------------------------------
  
//   /// تحديث استعلام البحث
//   void setSearchQuery(String query) {
//     state = state.copyWith(searchQuery: query);
//   }
  
//   /// تحديث فلتر حالة الرصيد
//   void setBalanceFilter(BalanceStatus? status) {
//     state = state.copyWith(filterStatus: status);
//   }
  
//   /// مسح جميع الفلاتر
//   void clearFilters() {
//     state = state.copyWith(
//       searchQuery: '',
//       filterStatus: null,
//     );
//   }
  
//   /// تحديث البيانات
//   Future<void> refresh() async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       // تحميل العملاء
//       final customers = await _repository.getAllCustomers();
      
//       // تحميل الإحصائيات
//       Map<String, dynamic>? statistics;
//       try {
//         statistics = await _repository.getCustomerStatistics();
//       } catch (e) {
//         AppUtils.logError('خطأ في تحميل إحصائيات العملاء', e);
//         // في حالة فشل تحميل الإحصائيات، نستخدم إحصائيات افتراضية
//         statistics = {
//           'total_count': customers.length,
//           'active_count': customers.where((c) => c.isActive).length,
//           'debt_count': customers.where((c) => c.hasDebt).length,
//           'credit_count': customers.where((c) => c.hasCredit).length,
//           'balanced_count': customers.where((c) => c.isBalanced).length,
//           'total_debt': customers.where((c) => c.hasDebt).fold(0.0, (sum, c) => sum + c.balance),
//           'total_credit': customers.where((c) => c.hasCredit).fold(0.0, (sum, c) => sum + c.absoluteBalance),
//         };
//       }
      
//       state = state.copyWith(
//         customers: customers,
//         statistics: statistics,
//         isLoading: false,
//       );
      
//       AppUtils.logInfo('تم تحديث العملاء بنجاح');
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث العملاء', e);
//     }
//   }
  
//   /// إنشاء عميل جديد
//   Future<bool> createCustomer({
//     required String name,
//     String? phone,
//     String? email,
//     String? address,
//     double balance = 0.0,
//     String? notes,
//   }) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       final customer = Customer.create(
//         id: AppUtils.generateId(),
//         name: name,
//         phone: phone,
//         email: email,
//         addres: address,
//         balance: balance,
//         notes: notes,
//       );
      
//       await _repository.createCustomer(customer);
//       await refresh();
      
//       AppUtils.logInfo('تم إنشاء عميل بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في إنشاء عميل', e);
//       return false;
//     }
//   }
  
//   /// تحديث عميل موجود
//   Future<bool> updateCustomer(Customer customer) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.updateCustomer(customer);
//       await refresh();
      
//       AppUtils.logInfo('تم تحديث عميل بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث عميل', e);
//       return false;
//     }
//   }
  
//   /// حذف عميل
//   Future<bool> deleteCustomer(String id) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.deleteCustomer(id);
//       await refresh();
      
//       AppUtils.logInfo('تم حذف عميل بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في حذف عميل', e);
//       return false;
//     }
//   }
  
//   // ------------------------------------------------------------------
//   // عمليات الرصيد - Balance Operations
//   // ------------------------------------------------------------------
  
//   /// تحديث رصيد العميل
//   Future<bool> updateCustomerBalance(String id, double newBalance) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.updateCustomerBalance(id, newBalance);
//       await refresh();
      
//       AppUtils.logInfo('تم تحديث رصيد العميل بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث رصيد العميل', e);
//       return false;
//     }
//   }
  
//   // ------------------------------------------------------------------
//   // الطرق المساعدة - Helper Methods
//   // ------------------------------------------------------------------
  
//   /// الحصول على عميل بالمعرف
//   Customer? getCustomerById(String id) {
//     try {
//       return state.customers.firstWhere((customer) => customer.id == id);
//     } catch (e) {
//       return null;
//     }
//   }
  
//   /// التحقق من وجود عميل بالاسم
//   bool isCustomerNameExists(String name, {String? excludeId}) {
//     return state.customers.any((customer) =>
//         customer.name.toLowerCase() == name.toLowerCase() &&
//         customer.id != excludeId);
//   }
  
//   /// الحصول على أفضل العملاء (حسب الرصيد)
//   List<Customer> getTopCustomers({int limit = 10}) {
//     final customers = state.customers.where((c) => c.isActive).toList();
    
//     customers.sort((a, b) => b.absoluteBalance.compareTo(a.absoluteBalance));
//     return customers.take(limit).toList();
//   }
// }

// /// حالة إدارة الموردين
// /// Supplier Management State
// class SupplierState {
//   final List<Supplier> suppliers;
//   final bool isLoading;
//   final String? error;
//   final String searchQuery;
//   final BalanceStatus? filterStatus;
//   final Map<String, dynamic>? statistics;
  
//   const SupplierState({
//     this.suppliers = const [],
//     this.isLoading = false,
//     this.error,
//     this.searchQuery = '',
//     this.filterStatus,
//     this.statistics,
//   });
  
//   SupplierState copyWith({
//     List<Supplier>? suppliers,
//     bool? isLoading,
//     String? error,
//     String? searchQuery,
//     BalanceStatus? filterStatus,
//     Map<String, dynamic>? statistics,
//   }) {
//     return SupplierState(
//       suppliers: suppliers ?? this.suppliers,
//       isLoading: isLoading ?? this.isLoading,
//       error: error,
//       searchQuery: searchQuery ?? this.searchQuery,
//       filterStatus: filterStatus ?? this.filterStatus,
//       statistics: statistics ?? this.statistics,
//     );
//   }
  
//   /// الحصول على الموردين المفلترة
//   List<Supplier> get filteredSuppliers {
//     var filtered = suppliers;
    
//     // فلترة حسب البحث
//     if (searchQuery.isNotEmpty) {
//       filtered = filtered.where((supplier) =>
//           supplier.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
//           (supplier.phone?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false) ||
//           (supplier.email?.toLowerCase().contains(searchQuery.toLowerCase()) ?? false))
//       .toList();
//     }
    
//     // فلترة حسب حالة الرصيد
//     if (filterStatus != null) {
//       filtered = filtered.where((supplier) => supplier.balanceStatus == filterStatus).toList();
//     }
    
//     return filtered;
//   }
  
//   /// عدد الموردين النشطين
//   int get activeSuppliersCount => 
//       suppliers.where((s) => s.isActive).length;
  
//   /// عدد الموردين المدينين
//   int get debtSuppliersCount => 
//       suppliers.where((s) => s.hasDebt).length;
  
//   /// عدد الموردين الدائنين
//   int get creditSuppliersCount => 
//       suppliers.where((s) => s.hasCredit).length;
  
//   /// إجمالي المبالغ المدينة
//   double get totalDebt => suppliers
//       .where((s) => s.hasDebt)
//       .fold(0.0, (sum, supplier) => sum + supplier.balance);
  
//   /// إجمالي المبالغ الدائنة
//   double get totalCredit => suppliers
//       .where((s) => s.hasCredit)
//       .fold(0.0, (sum, supplier) => sum + supplier.absoluteBalance);
// }

// /// مزود إدارة الموردين
// /// Supplier Management Provider
// class SupplierNotifier extends StateNotifier<SupplierState> {
//   final SupplierRepository _repository;
//   SupplierNotifier(this._repository) : super(const SupplierState());
  
//   // ------------------------------------------------------------------
//   // العمليات الأساسية - Basic Operations
//   // ------------------------------------------------------------------
  
//   /// تحديث استعلام البحث
//   void setSearchQuery(String query) {
//     state = state.copyWith(searchQuery: query);
//   }
  
//   /// تحديث فلتر حالة الرصيد
//   void setBalanceFilter(BalanceStatus? status) {
//     state = state.copyWith(filterStatus: status);
//   }
  
//   /// مسح جميع الفلاتر
//   void clearFilters() {
//     state = state.copyWith(
//       searchQuery: '',
//       filterStatus: null,
//     );
//   }
  
//   /// تحديث البيانات
//   Future<void> refresh() async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       // تحميل الموردين
//       final suppliers = await _repository.getAllSuppliers();
      
//       // تحميل الإحصائيات
//       Map<String, dynamic>? statistics;
//       try {
//         statistics = await _repository.getSupplierStatistics();
//       } catch (e) {
//         AppUtils.logError('خطأ في تحميل إحصائيات الموردين', e);
//         // في حالة فشل تحميل الإحصائيات، نستخدم إحصائيات افتراضية
//         statistics = {
//           'total_count': suppliers.length,
//           'active_count': suppliers.where((s) => s.isActive).length,
//           'debt_count': suppliers.where((s) => s.hasDebt).length,
//           'credit_count': suppliers.where((s) => s.hasCredit).length,
//           'balanced_count': suppliers.where((s) => s.isBalanced).length,
//           'total_debt': suppliers.where((s) => s.hasDebt).fold(0.0, (sum, s) => sum + s.balance),
//           'total_credit': suppliers.where((s) => s.hasCredit).fold(0.0, (sum, s) => sum + s.absoluteBalance),
//         };
//       }
      
//       state = state.copyWith(
//         suppliers: suppliers,
//         statistics: statistics,
//         isLoading: false,
//       );
      
//       AppUtils.logInfo('تم تحديث الموردين بنجاح');
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث الموردين', e);
//     }
//   }
  
//   /// إنشاء مورد جديد
//   Future<bool> createSupplier({
//     required String name,
//     String? phone,
//     String? email,
//     String? address,
//     double balance = 0.0,
//     String? notes,
//   }) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       final supplier = Account.create(
//         id: AppUtils.generateId(),
//         name: name,
//         type: AccountType.supplier,
//         phone: phone,
//         email: email,
//         address: address,
//         balance: balance,
//         notes: notes,
//       );
      
//       await _repository.createSupplier(supplier);
//       await refresh();
      
//       AppUtils.logInfo('تم إنشاء مورد بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في إنشاء مورد', e);
//       return false;
//     }
//   }
  
//   /// تحديث مورد موجود
//   Future<bool> updateSupplier(Account supplier) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.updateSupplier(supplier);
//       await refresh();
      
//       AppUtils.logInfo('تم تحديث مورد بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث مورد', e);
//       return false;
//     }
//   }
  
//   /// حذف مورد
//   Future<bool> deleteSupplier(String id) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.deleteSupplier(id);
//       await refresh();
      
//       AppUtils.logInfo('تم حذف مورد بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في حذف مورد', e);
//       return false;
//     }
//   }
  
//   // ------------------------------------------------------------------
//   // عمليات الرصيد - Balance Operations
//   // ------------------------------------------------------------------
  
//   /// تحديث رصيد المورد
//   Future<bool> updateSupplierBalance(String id, double newBalance) async {
//     try {
//       state = state.copyWith(isLoading: true, error: null);
      
//       await _repository.updateSupplierBalance(id, newBalance);
//       await refresh();
      
//       AppUtils.logInfo('تم تحديث رصيد المورد بنجاح');
//       return true;
//     } catch (e) {
//       state = state.copyWith(
//         isLoading: false,
//         error: ErrorHandler.handleError(e),
//       );
//       AppUtils.logError('خطأ في تحديث رصيد المورد', e);
//       return false;
//     }
//   }
  
//   // ------------------------------------------------------------------
//   // الطرق المساعدة - Helper Methods
//   // ------------------------------------------------------------------
  
//   /// الحصول على مورد بالمعرف
//   Account? getSupplierById(String id) {
//     try {
//       return state.suppliers.firstWhere((supplier) => supplier.id == id);
//     } catch (e) {
//       return null;
//     }
//   }
  
//   /// التحقق من وجود مورد بالاسم
//   bool isSupplierNameExists(String name, {String? excludeId}) {
//     return state.suppliers.any((supplier) =>
//         supplier.name.toLowerCase() == name.toLowerCase() &&
//         supplier.id != excludeId);
//   }
  
//   /// الحصول على أفضل الموردين (حسب الرصيد)
//   List<Account> getTopSuppliers({int limit = 10}) {
//     final suppliers = state.suppliers.where((s) => s.isActive).toList();
    
//     suppliers.sort((a, b) => b.absoluteBalance.compareTo(a.absoluteBalance));
//     return suppliers.take(limit).toList();
//   }
// }

// // ------------------------------------------------------------------
// // Providers - مزودات الخدمة
// // ------------------------------------------------------------------

// /// مزود قاعدة البيانات
// final databaseProvider = Provider<DatabaseHelper>((ref) {
//   return DatabaseHelper();
// });

// /// مزود DAO العملاء
// final customerDaoProvider = Provider<CustomerDao>((ref) {
//   final databaseHelper = ref.watch(databaseProvider);
//   return CustomerDao(databaseHelper);
// });

// /// مزود DAO الموردين
// final supplierDaoProvider = Provider<SupplierDao>((ref) {
//   final databaseHelper = ref.watch(databaseProvider);
//   return SupplierDao(databaseHelper);
// });

// /// مزود مستودع العملاء
// final customerRepositoryProvider = Provider<CustomerRepository>((ref) {
//   final dao = ref.watch(customerDaoProvider);
//   return CustomerRepository(dao);
// });

// /// مزود مستودع الموردين
// final supplierRepositoryProvider = Provider<SupplierRepository>((ref) {
//   final dao = ref.watch(supplierDaoProvider);
//   return SupplierRepository(dao);
// });

// /// مزود إدارة العملاء
// final customerProvider = StateNotifierProvider<CustomerNotifier, CustomerState>((ref) {
//   final repository = ref.watch(customerRepositoryProvider);
//   return CustomerNotifier(repository);
// });

// /// مزود إدارة الموردين
// final supplierProvider = StateNotifierProvider<SupplierNotifier, SupplierState>((ref) {
//   final repository = ref.watch(supplierRepositoryProvider);
//   return SupplierNotifier(repository);
// });