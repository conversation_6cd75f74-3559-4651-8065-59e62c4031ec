import '../local/dao/stock_dao.dart';
import '../local/dao/stock_movement_dao.dart';
import '../models/stock.dart';
import '../models/stock_movement.dart';
import '../../core/utils/app_utils.dart';
import '../../core/exceptions/app_exceptions.dart';

class StockRepository {
  final StockDao _stockDao = StockDao();
  final StockMovementDao _stockMovementDao = StockMovementDao();

  // ------------------------------------------------------------------
  // CRUD Operations
  // ------------------------------------------------------------------

  /// إنشاء مخزون جديد
  Future<String> createStock(Stock stock) async {
    try {
      return await _stockDao.insert(stock);
    } catch (e) {
      AppUtils.logError('Error creating stock', e);
      rethrow;
    }
  }

  /// استرجاع جميع المخزون
  Future<List<Stock>> getAllStocks({
    String? warehouseId,
    String? productId,
    String? categoryId,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _stockDao.getStocksWithDetails(
        warehouseId: warehouseId,
        productId: productId,
        categoryId: categoryId,
        orderBy: orderBy,
        ascending: ascending,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      AppUtils.logError('Error getting all stocks', e);
      throw RepositoryException('Failed to get stocks');
    }
  }

  /// استرجاع مخزون بالمعرف
  Future<Stock?> getStockById(String id) async {
    try {
      return await _stockDao.findById(id);
    } catch (e) {
      AppUtils.logError('Error getting stock by id', e);
      throw RepositoryException('Failed to get stock');
    }
  }

  /// استرجاع مخزون حسب المنتج والمخزن
  Future<Stock?> getStockByProductAndWarehouse(
      String productId, String warehouseId) async {
    try {
      return await _stockDao.getStockByProductAndWarehouse(
          productId, warehouseId);
    } catch (e) {
      AppUtils.logError('Error getting stock by product and warehouse', e);
      return null;
    }
  }

  /// تحديث مخزون
  Future<bool> updateStock(String id, Stock stock) async {
    try {
      return await _stockDao.update(id, stock);
    } catch (e) {
      AppUtils.logError('Error updating stock', e);
      rethrow;
    }
  }

  /// حذف مخزون
  Future<bool> deleteStock(String id) async {
    try {
      final stock = await _stockDao.findById(id);
      if (stock == null) return false;

      final deletedStock = stock.copyWith(
        updatedAt: DateTime.now(),

        // يراجع
        expiryDate: DateTime.now(),
        isSynced: false,
      );
      return await _stockDao.update(id, deletedStock);
    } catch (e) {
      AppUtils.logError('Error deleting stock', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Stock Movement Operations
  // ------------------------------------------------------------------

  /// تحديث المخزون مع إنشاء حركة
  Future<bool> updateStockWithMovement({
    required String productId,
    required String warehouseId,
    required String movementType,
    required double quantity,
    required double unitCost,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? fromWarehouseId,
    String? toWarehouseId,
    String? batchNumber,
    DateTime? expiryDate,
    String? notes,
    String? reason,
    required String createdBy,
    String? branchId,
  }) async {
    try {
      // Get current stock
      final currentStock =
          await getStockByProductAndWarehouse(productId, warehouseId);

      // Calculate new quantity based on movement type
      double newQuantity = 0;
      if (currentStock != null) {
        newQuantity = currentStock.quantity;
      }

      switch (movementType) {
        case 'in':
          newQuantity += quantity;
          break;
        case 'out':
          newQuantity -= quantity;
          if (newQuantity < 0) {
            throw ValidationException('Insufficient stock quantity');
          }
          break;
        case 'adjustment':
          // For adjustments, we treat them as in/out movements
          // The quantity passed should already be the movement amount
          newQuantity += quantity; // quantity can be positive or negative
          if (newQuantity < 0) {
            throw ValidationException(
                'Insufficient stock quantity after adjustment');
          }
          break;
        case 'transfer':
          if (warehouseId == fromWarehouseId) {
            // Outbound from source warehouse
            newQuantity -= quantity;
            if (newQuantity < 0) {
              throw ValidationException('Insufficient stock for transfer');
            }
          } else if (warehouseId == toWarehouseId) {
            // Inbound to destination warehouse
            newQuantity += quantity;
          }
          break;
      }

      // Update or create stock
      if (currentStock != null) {
        final updatedStock = currentStock.updateQuantity(
          newQuantity: newQuantity,
          newAverageCost: _calculateNewAverageCost(
            currentStock.quantity,
            currentStock.averageCost,
            quantity,
            unitCost,
            movementType,
          ),
        );
        await updateStock(currentStock.id, updatedStock);
      } else {
        // Create new stock record
        final newStock = Stock.create(
          id: AppUtils.generateId(),
          productId: productId,
          warehouseId: warehouseId,
          quantity: newQuantity,
          averageCost: unitCost,
          batchNumber: batchNumber,
          expiryDate: expiryDate,
        );
        await createStock(newStock);
      }

      // Create stock movement record
      await _stockMovementDao.createMovement(
        productId: productId,
        warehouseId: warehouseId,
        movementType: movementType,
        quantity: quantity,
        unitCost: unitCost,
        referenceType: referenceType,
        referenceId: referenceId,
        referenceNumber: referenceNumber,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        notes: notes,
        reason: reason,
        createdBy: createdBy,
        branchId: branchId,
      );

      return true;
    } catch (e) {
      AppUtils.logError('Error updating stock with movement', e);
      rethrow;
    }
  }

  /// نقل المخزون بين المخازن
  Future<bool> transferStock({
    required String productId,
    required String fromWarehouseId,
    required String toWarehouseId,
    required double quantity,
    required double unitCost,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? batchNumber,
    DateTime? expiryDate,
    String? notes,
    String? reason,
    required String createdBy,
    String? branchId,
  }) async {
    try {
      // Check source warehouse stock
      final sourceStock =
          await getStockByProductAndWarehouse(productId, fromWarehouseId);
      if (sourceStock == null ||
          sourceStock.actualAvailableQuantity < quantity) {
        throw ValidationException('Insufficient stock for transfer');
      }

      // Update source warehouse (outbound)
      await updateStockWithMovement(
        productId: productId,
        warehouseId: fromWarehouseId,
        movementType: 'transfer',
        quantity: quantity,
        unitCost: unitCost,
        referenceType: referenceType,
        referenceId: referenceId,
        referenceNumber: referenceNumber,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        notes: notes,
        reason: reason,
        createdBy: createdBy,
        branchId: branchId,
      );

      // Update destination warehouse (inbound)
      await updateStockWithMovement(
        productId: productId,
        warehouseId: toWarehouseId,
        movementType: 'transfer',
        quantity: quantity,
        unitCost: unitCost,
        referenceType: referenceType,
        referenceId: referenceId,
        referenceNumber: referenceNumber,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        notes: notes,
        reason: reason,
        createdBy: createdBy,
        branchId: branchId,
      );

      return true;
    } catch (e) {
      AppUtils.logError('Error transferring stock', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Stock Analysis
  // ------------------------------------------------------------------

  /// استرجاع المخزون المنخفض
  Future<List<Stock>> getLowStockItems() async {
    try {
      return await _stockDao.getLowStockItems();
    } catch (e) {
      AppUtils.logError('Error getting low stock items', e);
      return [];
    }
  }

  /// استرجاع المخزون المنتهي الصلاحية
  Future<List<Stock>> getExpiredStockItems() async {
    try {
      return await _stockDao.getExpiredStockItems();
    } catch (e) {
      AppUtils.logError('Error getting expired stock items', e);
      return [];
    }
  }

  /// استرجاع المخزون قريب الانتهاء
  Future<List<Stock>> getNearExpiryStockItems({int daysAhead = 30}) async {
    try {
      return await _stockDao.getNearExpiryStockItems(daysAhead: daysAhead);
    } catch (e) {
      AppUtils.logError('Error getting near expiry stock items', e);
      return [];
    }
  }

  /// استرجاع إحصائيات المخزون
  Future<Map<String, dynamic>> getStockStatistics() async {
    try {
      return await _stockDao.getStockStatistics();
    } catch (e) {
      AppUtils.logError('Error getting stock statistics', e);
      throw RepositoryException('Failed to get stock statistics');
    }
  }

  // ------------------------------------------------------------------
  // Helper Methods
  // ------------------------------------------------------------------

  /// حساب متوسط التكلفة الجديد
  double _calculateNewAverageCost(
    double currentQuantity,
    double currentAverageCost,
    double newQuantity,
    double newUnitCost,
    String movementType,
  ) {
    if (movementType == 'out' || movementType == 'adjustment') {
      return currentAverageCost; // Keep current average cost for outbound movements
    }

    if (currentQuantity == 0) {
      return newUnitCost;
    }

    final totalCurrentValue = currentQuantity * currentAverageCost;
    final totalNewValue = newQuantity * newUnitCost;
    final totalQuantity = currentQuantity + newQuantity;

    return (totalCurrentValue + totalNewValue) / totalQuantity;
  }
}
