import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:tijari_tech/features/customers_suppliers/manages_customer_suppliers_page.dart';
import 'package:tijari_tech/features/customers_suppliers/customers_page.dart';
import 'package:tijari_tech/features/customers_suppliers/suppliers_page.dart';
import 'package:tijari_tech/features/customers_suppliers/customer_add_page.dart';
import 'package:tijari_tech/features/customers_suppliers/supplier_add_page.dart';
import 'package:tijari_tech/features/customers_suppliers/customer_edit_page.dart';
import 'package:tijari_tech/features/customers_suppliers/supplier_edit_page.dart';
import 'package:tijari_tech/features/cash/cash_page.dart';
import 'package:tijari_tech/features/cash/cash_boxes_page.dart';
import 'package:tijari_tech/features/cash/cash_box_form_page.dart';
import 'package:tijari_tech/features/cash/cash_flow_report_page.dart';
import 'package:tijari_tech/features/cash/transactions_page.dart';
import 'package:tijari_tech/features/categories/add_edite_category_dialog.dart';
import 'package:tijari_tech/features/dashboard/dashboard_page.dart';
import 'package:tijari_tech/features/employees/employees_page.dart';
import 'package:tijari_tech/features/inventory/inventory_page.dart';
import 'package:tijari_tech/features/pos/pos_page.dart';
import 'package:tijari_tech/features/categories/categories_management_page.dart';
import 'package:tijari_tech/features/purchases/purchases_page.dart';
import 'package:tijari_tech/features/reports/reports_page.dart';
import 'package:tijari_tech/features/sales/sale_add_page.dart';
import 'package:tijari_tech/features/sales/sales_page.dart';
import 'package:tijari_tech/features/sales/enhanced_sales_list_page.dart';
import 'package:tijari_tech/features/purchases/purchases_list_page.dart';
import 'package:tijari_tech/features/purchases/purchase_view_page.dart';
import 'package:tijari_tech/features/purchases/purchase_add_page.dart';
import 'package:tijari_tech/features/purchases/purchase_edit_page.dart';
import 'package:tijari_tech/features/sales/sale_view_page.dart';
import 'package:tijari_tech/features/sales/sale_edit_page.dart';
import 'package:tijari_tech/features/settings/settings_page.dart';
import 'package:tijari_tech/features/units/add_unit_dialog.dart';
import 'package:tijari_tech/features/units/units_management_page.dart';
import 'package:tijari_tech/router/routes.dart';

import '../features/products/enhanced_products_page.dart';

import '../features/settings/user_management_page.dart';
import '../features/ai/ai_dashboard_page.dart';

import '../features/products/add_edit_product_page.dart';
import '../features/inventory/stock_adjustment_page.dart';
import '../features/sales/sales_return_page.dart';
import '../features/cash/receipt_voucher_page.dart';
import '../features/reports/profit_loss_report_page.dart';
import '../features/reports/sales_purchases_reports_page.dart';
import '../features/reports/profit_loss_reports_page.dart';
import '../features/reports/customer_supplier_reports_page.dart';
import '../features/reports/financial_dashboard_page.dart';
import '../features/inventory/warehouses_page.dart';
import '../features/inventory/stock_management_page.dart';
import '../features/inventory/stock_movements_page.dart';
import '../features/inventory/warehouse_form_page.dart';
import '../features/inventory/warehouse_details_page.dart';

// Router configuration
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: AppRoutes.dashboard,
    debugLogDiagnostics: true,
    routes: [
      // Dashboard Route
      GoRoute(
        path: AppRoutes.dashboard,
        name: 'dashboard',
        builder: (context, state) => const DashboardPage(),
      ),
      // Inventory Routes
      GoRoute(
        path: AppRoutes.inventory,
        name: 'inventory',
        builder: (context, state) => const InventoryPage(),
        routes: [
          // Products
          GoRoute(
            path: '/products',
            name: 'products',
            builder: (context, state) => const EnhancedProductsPage(),
            routes: [
              // إضافة منتج جديد
              GoRoute(
                path: '/add',
                name: 'product-add',
                builder: (context, state) => const AddEditProductPage(
                  isEdit: false,
                ),
              ),
              // تعديل منتج موجود
              GoRoute(
                path: '/edit/:id',
                name: 'product-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return AddEditProductPage(
                    isEdit: true,
                    productId: id,
                  );
                },
              ),
              GoRoute(
                path: '/view/:id',
                name: 'product-view',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return Scaffold(
                    body: Center(child: Text('عرض منتج: $id')),
                  );
                },
              ),
            ],
          ),

/*
          // Products
          GoRoute(
            path: '/products',
            name: 'products',
            builder: (context, state) => const ProductsPage(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'product-add',
                builder: (context, state) => const ProductAddPage(),
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'product-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return Scaffold(
                    body: Center(child: Text('تعديل منتج: $id')),
                  );
                },
              ),
              GoRoute(
                path: '/view/:id',
                name: 'product-view',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return Scaffold(
                    body: Center(child: Text('عرض منتج: $id')),
                  );
                },
              ),
            ],
          ),
*/
          // Categories - إدارة الفئات
          GoRoute(
            path: '/categories',
            name: 'categories',
            builder: (context, state) => CategoriesManagementPage(),
            routes: [
              // إضافة فئة جديدة أو فئة فرعية
              GoRoute(
                path: 'add',
                name: 'category-add',
                builder: (context, state) {
                  // التحقق من وجود معرف الفئة الأب في query parameters
                  final parentId = state.uri.queryParameters['parentId'];
                  print('parentCategoryId:============== $parentId');

                  // final parentId = state.uri.queryParameters['parent_id'];
                  print('parentCategoryId:============== ${parentId}');
                  return AddEditedCategoryDialog(
                    isEdit: false,
                    parentCategoryId: parentId,
                  );
                },
              ),
              // تعديل فئة موجودة
              GoRoute(
                path: 'edit/:id',
                name: 'category-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id'];
                  return AddEditedCategoryDialog(
                    isEdit: true,
                    categoryId: id,
                  );
                },
              ),
            ],
          ), // Units
          GoRoute(
            path: '/units',
            name: 'units',
            builder: (context, state) => UnitsManagementPage(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'unit-add',
                builder: (context, state) {
                  // التحقق من وجود معرف الفئة الأب في query parameters
                  final parentId = state.uri.queryParameters['parentId'];
                  print('parentUnitId:============== $parentId');

                  // final parentId = state.uri.queryParameters['parent_id'];
                  print('parentUnitId:============== ${parentId}');
                  return AddEditUnitDialog(
                      // isEdit: false,
                      // parentUnitId: parentId,
                      );
                },
                // AddUnitDialog(),
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'unit-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return AddEditUnitDialog(
                      // isEdit: false,
                      // parentUnitId: id,
                      );
                },
              ),
            ],
          ),

          // Warehouses
          GoRoute(
            path: '/warehouses',
            name: 'warehouses',
            builder: (context, state) => const WarehousesPage(),
            routes: [
              GoRoute(
                path: '/add',
                name: 'warehouse-add',
                builder: (context, state) => const WarehouseFormPage(),
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'warehouse-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return WarehouseFormPage(warehouseId: id);
                },
              ),
              GoRoute(
                path: '/view/:id',
                name: 'warehouse-view',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return WarehouseDetailsPage(warehouseId: id);
                },
              ),
            ],
          ),

          // Stock Management
          GoRoute(
            path: '/stock-management',
            name: 'stock-management',
            builder: (context, state) => const StockManagementPage(),
          ),

          // Stock Movements
          GoRoute(
            path: '/stock-movements',
            name: 'stock-movements',
            builder: (context, state) => const StockMovementsPage(),
          ),

          // Stock Log
          GoRoute(
            path: '/stock-log',
            name: 'stock-log',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('سجل المخزون')),
            ),
          ),

          // Stock Adjustment
          GoRoute(
            path: '/stock-adjustment',
            name: 'stock-adjustment',
            builder: (context, state) => const StockAdjustmentPage(),
          ),
        ],
      ),

      // Sales Routes
      GoRoute(
        path: AppRoutes.sales,
        name: 'sales',
        builder: (context, state) => const EnhancedSalesListPage(),
        routes: [
          // POS
          GoRoute(
            path: '/pos',
            name: 'pos',
            builder: (context, state) => const POSPage(),
          ),

          // Sale Add
          GoRoute(
            path: '/add',
            name: 'sale-add',
            builder: (context, state) => const SaleAddPage(),
          ),

          // Sales List
          GoRoute(
            path: '/list',
            name: 'sale-list',
            builder: (context, state) => const EnhancedSalesListPage(),
          ),

          // Sale View
          GoRoute(
            path: '/view/:id',
            name: 'sale-view',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return SaleViewPage(saleId: id);
            },
          ),

          // Sale Edit
          GoRoute(
            path: '/edit/:id',
            name: 'sale-edit',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return SaleEditPage(saleId: id);
            },
          ),

          // Sales Return
          GoRoute(
            path: '/return',
            name: 'sales-return',
            builder: (context, state) => const SalesReturnPage(),
          ),
        ],
      ),
// الصفحة الرئيسية للحسابات
      GoRoute(
        path: AppRoutes.accounts,
        builder: (context, state) => const ManagesCustomerSuppliersPage(),
      ),

      // صفحة العملاء
      GoRoute(
        path: AppRoutes.customers,
        builder: (context, state) => const CustomersPage(),
        routes: [
          // إضافة عميل
          GoRoute(
            path: '/add',
            builder: (context, state) => const CustomerAddPage(),
          ),
          // تعديل عميل
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return CustomerEditPage(customerId: id);
            },
          ),
        ],
      ),

      // صفحة الموردين
      GoRoute(
        path: AppRoutes.suppliers,
        builder: (context, state) => const SuppliersPage(),
        routes: [
          // إضافة مورد
          GoRoute(
            path: '/add',
            builder: (context, state) => const SupplierAddPage(),
          ),
          // تعديل مورد
          GoRoute(
            path: '/edit/:id',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return SupplierEditPage(supplierId: id);
            },
          ),
        ],
      ),

      // // Accounts Routes
      // GoRoute(
      //   path: AppRoutes.accounts,
      //   name: 'accounts',
      //   builder: (context, state) => const AccountsPage(),
      //   routes: [
      //     // النظام الموحد للعملاء
      //     GoRoute(
      //       path: '/customers-unified',
      //       name: 'customers-unified',
      //       builder: (context, state) => const CustomersPage(),
      //     ),

      //     // النظام الموحد للموردين
      //     GoRoute(
      //       path: '/suppliers-unified',
      //       name: 'suppliers-unified',
      //       builder: (context, state) => const SuppliersPage(),
      //     ),

      //     // // صفحة الإضافة/التعديل الموحدة
      //     // GoRoute(
      //     //   path: '/add-edit',
      //     //   name: 'add-edit-account',
      //     //   builder: (context, state) {
      //     //     final type = state.uri.queryParameters['type'];
      //     //     final id = state.uri.queryParameters['id'];

      //     //     if (type == null) {
      //     //       return const Scaffold(
      //     //         body: Center(child: Text('نوع الحساب مطلوب')),
      //     //       );
      //     //     }

      //     //     final accountType = type == 'customer'
      //     //         ? AccountType.customer
      //     //         : AccountType.supplier;

      //     //     return AddEditAccountPage(
      //     //       accountType: accountType,
      //     //       accountId: id,
      //     //     );
      //     //   },
      //     // ),

      //     // الحفاظ على المسارات القديمة للتوافق مع الإصدارات السابقة
      //     // Customers (Legacy)
      //     GoRoute(
      //       path: '/customers',
      //       name: 'customers',
      //       builder: (context, state) => const CustomersPage(),
      //       routes: [
      //         // GoRoute(
      //         //   path: '/add',
      //         //   name: 'customer-add',
      //         //   builder: (context, state) => const AddEditAccountPage(
      //         //     accountType: AccountType.customer,
      //         //   ),
      //         // ),
      //         // GoRoute(
      //         //   path: '/edit/:id',
      //         //   name: 'customer-edit',
      //         //   builder: (context, state) {
      //         //     final id = state.pathParameters['id']!;
      //         //     return AddEditAccountPage(
      //         //       accountType: AccountType.customer,
      //         //       accountId: id,
      //         //     );
      //         //   },
      //         // ),
      //         GoRoute(
      //           path: '/view/:id',
      //           name: 'customer-view',
      //           builder: (context, state) {
      //             final id = state.pathParameters['id']!;
      //             return Scaffold(
      //               body: Center(child: Text('عرض عميل: $id')),
      //             );
      //           },
      //         ),
      //       ],
      //     ),

      //     // Suppliers (Legacy)
      //     GoRoute(
      //       path: '/suppliers',
      //       name: 'suppliers',
      //       builder: (context, state) => const SuppliersPage(),
      //       routes: [
      //         // GoRoute(
      //         //   path: '/add',
      //         //   name: 'supplier-add',
      //         //   builder: (context, state) => const AddEditAccountPage(
      //         //     accountType: AccountType.supplier,
      //         //   ),
      //         // ),
      //         // GoRoute(
      //         //   path: '/edit/:id',
      //         //   name: 'supplier-edit',
      //         //   builder: (context, state) {
      //         //     final id = state.pathParameters['id']!;
      //         //     return AddEditAccountPage(
      //         //       accountType: AccountType.supplier,
      //         //       accountId: id,
      //         //     );
      //         //   },
      //         // ),
      //         GoRoute(
      //           path: '/view/:id',
      //           name: 'supplier-view',
      //           builder: (context, state) {
      //             final id = state.pathParameters['id']!;
      //             return Scaffold(
      //               body: Center(child: Text('عرض مورد: $id')),
      //             );
      //           },
      //         ),
      //       ],
      //     ),
      //   ],
      // ),

      // Purchase Routes
      GoRoute(
        path: AppRoutes.purchases,
        name: 'purchases',
        builder: (context, state) => const PurchasesListPage(),
        routes: [
          // Purchase List
          GoRoute(
            path: '/list',
            name: 'purchase-list',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('قائمة المشتريات')),
            ),
          ),

          // Purchase Add
          GoRoute(
            path: '/add',
            name: 'purchase-add',
            builder: (context, state) => const PurchaseAddPage(),
          ),

          // Purchase View
          GoRoute(
            path: '/view/:id',
            name: 'purchase-view',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return PurchaseViewPage(purchaseId: id);
            },
          ),

          // Purchase Edit
          GoRoute(
            path: '/edit/:id',
            name: 'purchase-edit',
            builder: (context, state) {
              final id = state.pathParameters['id']!;
              return PurchaseEditPage(purchaseId: id);
            },
          ),
        ],
      ),

      // Cash Routes
      GoRoute(
        path: AppRoutes.cash,
        name: 'cash',
        builder: (context, state) => const CashPage(),
        routes: [
          // Cash Boxes
          GoRoute(
            path: '/boxes',
            name: 'cash-boxes',
            builder: (context, state) => const CashBoxesPage(),
            routes: [
              // Add Cash Box
              GoRoute(
                path: '/add',
                name: 'cash-box-add',
                builder: (context, state) => const CashBoxFormPage(),
              ),
              // Edit Cash Box
              GoRoute(
                path: '/edit',
                name: 'cash-box-edit',
                builder: (context, state) {
                  final cashBoxId = state.uri.queryParameters['id'];
                  return CashBoxFormPage(cashBoxId: cashBoxId);
                },
              ),
            ],
          ),

          // Transactions
          GoRoute(
            path: '/transactions',
            name: 'transactions',
            builder: (context, state) => const TransactionsPage(),
          ),

          // Receipt
          GoRoute(
            path: '/receipt',
            name: 'receipt',
            builder: (context, state) => const ReceiptVoucherPage(),
          ),

          // Payment
          GoRoute(
            path: '/payment',
            name: 'payment',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('إيصال صرف')),
            ),
          ),

          // Cash Flow Report
          GoRoute(
            path: '/reports/flow',
            name: 'cash-flow-report',
            builder: (context, state) => const CashFlowReportPage(),
          ),
        ],
      ),

      // Employees Routes
      GoRoute(
        path: AppRoutes.employees,
        name: 'employees',
        builder: (context, state) => const EmployeesPage(),
        routes: [
          // Employee List
          GoRoute(
            path: '/list',
            name: 'employee-list',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('قائمة الموظفين')),
            ),
          ),

          // Employee Add
          GoRoute(
            path: '/add',
            name: 'employee-add',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('إضافة موظف')),
            ),
          ),

          // Salaries
          GoRoute(
            path: '/salaries',
            name: 'salaries',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('إدارة الرواتب')),
            ),
          ),
        ],
      ),

      // Reports Routes
      GoRoute(
        path: AppRoutes.reports,
        name: 'reports',
        builder: (context, state) => const ReportsPage(),
        routes: [
          // Financial Dashboard
          GoRoute(
            path: '/dashboard',
            name: 'financial-dashboard',
            builder: (context, state) => const FinancialDashboardPage(),
          ),

          // Sales and Purchases Reports
          GoRoute(
            path: '/sales-purchases',
            name: 'sales-purchases-reports',
            builder: (context, state) => const SalesPurchasesReportsPage(),
          ),

          // Profit and Loss Reports
          GoRoute(
            path: '/profit-loss',
            name: 'profit-loss-reports',
            builder: (context, state) => const ProfitLossReportsPage(),
          ),

          // Customer and Supplier Reports
          GoRoute(
            path: '/accounts',
            name: 'accounts-reports',
            builder: (context, state) => const CustomerSupplierReportsPage(),
          ),

          // Legacy Sales Report
          GoRoute(
            path: '/sales',
            name: 'sales-report',
            builder: (context, state) => const SalesPurchasesReportsPage(),
          ),

          // Legacy Purchase Report
          GoRoute(
            path: '/purchases',
            name: 'purchase-report',
            builder: (context, state) => const SalesPurchasesReportsPage(),
          ),

          // Inventory Report
          GoRoute(
            path: '/inventory',
            name: 'inventory-report',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('تقرير المخزون')),
            ),
          ),

          // Customer Report
          GoRoute(
            path: '/customers',
            name: 'customer-report',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('تقرير العملاء')),
            ),
          ),

          // Profit Loss Report
          GoRoute(
            path: '/profit-loss',
            name: 'profit-loss-report',
            builder: (context, state) => const ProfitLossReportPage(),
          ),

          // Cash Flow Report
          // GoRoute(
          //   path: '/cash-flow',
          //   name: 'cash-flow-report',
          //   builder: (context, state) => const Scaffold(
          //     body: Center(child: Text('تقرير التدفق النقدي')),
          //   ),
          // ),
        ],
      ),

      // Settings Routes
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsPage(),
        routes: [
          // User Management
          GoRoute(
            path: '/users',
            name: 'user-management',
            builder: (context, state) => const UserManagementPage(),
          ),

          // AI Dashboard
          GoRoute(
            path: '/ai-dashboard',
            name: 'ai-dashboard',
            builder: (context, state) => const AIDashboardPage(),
          ),

          // Company Info
          GoRoute(
            path: '/company',
            name: 'company-info',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('معلومات الشركة')),
            ),
          ),

          // Printer Settings
          GoRoute(
            path: '/printer',
            name: 'printer-settings',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('إعدادات الطابعة')),
            ),
          ),

          // Tax Settings
          GoRoute(
            path: '/tax',
            name: 'tax-settings',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('إعدادات الضريبة')),
            ),
          ),

          // Backup & Restore
          GoRoute(
            path: '/backup',
            name: 'backup-restore',
            builder: (context, state) => const Scaffold(
              body: Center(child: Text('النسخ الاحتياطي والاستعادة')),
            ),
          ),
        ],
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('خطأ'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.uri}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});
