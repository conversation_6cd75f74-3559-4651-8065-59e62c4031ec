import 'package:json_annotation/json_annotation.dart';
import 'package:tijari_tech/core/constants/database_constants.dart';

part 'stock_movement.g.dart';

@JsonSerializable()
class StockMovement {
  final String id;
  final String productId;
  final String warehouseId;
  final String movementType; // in, out, transfer, adjustment
  final double quantity;
  final double unitCost;
  final double totalValue;
  final String? referenceType; // sale, purchase, adjustment, transfer
  final String? referenceId; // ID of the related transaction
  final String? referenceNumber; // Reference number for tracking
  final String? fromWarehouseId; // For transfers
  final String? toWarehouseId; // For transfers
  final String? batchNumber;
  final DateTime? expiryDate;
  final String? notes;
  final String? reason;
  final String createdBy;
  final DateTime createdAt;
  final String? branchId;
  final bool isSynced;

  // Related data (not stored in main table)
  final String? productName;
  final String? productBarcode;
  final String? warehouseName;
  final String? fromWarehouseName;
  final String? toWarehouseName;
  final String? createdByName;

  const StockMovement({
    required this.id,
    required this.productId,
    required this.warehouseId,
    required this.movementType,
    required this.quantity,
    required this.unitCost,
    required this.totalValue,
    this.referenceType,
    this.referenceId,
    this.referenceNumber,
    this.fromWarehouseId,
    this.toWarehouseId,
    this.batchNumber,
    this.expiryDate,
    this.notes,
    this.reason,
    required this.createdBy,
    required this.createdAt,
    this.branchId,
    this.isSynced = false,
    this.productName,
    this.productBarcode,
    this.warehouseName,
    this.fromWarehouseName,
    this.toWarehouseName,
    this.createdByName,
  });

  // JSON serialization
  factory StockMovement.fromJson(Map<String, dynamic> json) =>
      _$StockMovementFromJson(json);
  Map<String, dynamic> toJson() => _$StockMovementToJson(this);

  // Database mapping
  factory StockMovement.fromMap(Map<String, dynamic> map) {
    return StockMovement(
      id: map[DatabaseConstants.columnStockMovementId] as String,
      productId: map[DatabaseConstants.columnStockMovementProductId] as String,
      warehouseId:
          map[DatabaseConstants.columnStockMovementWarehouseId] as String,
      movementType: map[DatabaseConstants.columnStockMovementType] as String,
      quantity: (map[DatabaseConstants.columnStockMovementQuantity] as num)
          .toDouble(),
      unitCost: (map[DatabaseConstants.columnStockMovementUnitCost] as num)
          .toDouble(),
      totalValue: (map[DatabaseConstants.columnStockMovementTotalValue] as num)
          .toDouble(),
      referenceType:
          map[DatabaseConstants.columnStockMovementReferenceType] as String?,
      referenceId:
          map[DatabaseConstants.columnStockMovementReferenceId] as String?,
      referenceNumber: map['reference_number'] as String?,
      fromWarehouseId: map['from_warehouse_id'] as String?,
      toWarehouseId: map['to_warehouse_id'] as String?,
      batchNumber: map['batch_number'] as String?,
      expiryDate: map['expiry_date'] != null
          ? DateTime.parse(map['expiry_date'] as String)
          : null,
      notes: map[DatabaseConstants.columnStockMovementNotes] as String?,
      reason: map['reason'] as String?,
      createdBy: map[DatabaseConstants.columnStockMovementCreatedBy] as String,
      createdAt: DateTime.parse(
          map[DatabaseConstants.columnStockMovementCreatedAt] as String),
      branchId: map[DatabaseConstants.columnStockMovementBranchId] as String?,
      isSynced:
          (map[DatabaseConstants.columnStockMovementIsSynced] as int? ?? 0) ==
              1,
      productName: map['product_name'] as String?,
      productBarcode: map['product_barcode'] as String?,
      warehouseName: map['warehouse_name'] as String?,
      fromWarehouseName: map['from_warehouse_name'] as String?,
      toWarehouseName: map['to_warehouse_name'] as String?,
      createdByName: map['created_by_name'] as String?,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      DatabaseConstants.columnStockMovementId: id,
      DatabaseConstants.columnStockMovementProductId: productId,
      DatabaseConstants.columnStockMovementWarehouseId: warehouseId,
      DatabaseConstants.columnStockMovementType: movementType,
      DatabaseConstants.columnStockMovementQuantity: quantity,
      DatabaseConstants.columnStockMovementQty: quantity, // استخدام نفس القيمة
      DatabaseConstants.columnStockMovementUnitCost: unitCost,
      DatabaseConstants.columnStockMovementTotalValue: totalValue,
      DatabaseConstants.columnStockMovementReferenceType: referenceType,
      DatabaseConstants.columnStockMovementReferenceId: referenceId,
      'reference_number': referenceNumber,
      'from_warehouse_id': fromWarehouseId,
      'to_warehouse_id': toWarehouseId,
      'batch_number': batchNumber,
      'expiry_date': expiryDate?.toIso8601String(),
      DatabaseConstants.columnStockMovementNotes: notes,
      'reason': reason,
      DatabaseConstants.columnStockMovementCreatedBy: createdBy,
      DatabaseConstants.columnStockMovementCreatedAt:
          createdAt.toIso8601String(),
      DatabaseConstants.columnStockMovementBranchId: branchId,
      DatabaseConstants.columnStockMovementIsSynced: isSynced ? 1 : 0,
    };
  }

  // Copy with method
  StockMovement copyWith({
    String? id,
    String? productId,
    String? warehouseId,
    String? movementType,
    double? quantity,
    double? unitCost,
    double? totalValue,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? fromWarehouseId,
    String? toWarehouseId,
    String? batchNumber,
    DateTime? expiryDate,
    String? notes,
    String? reason,
    String? createdBy,
    DateTime? createdAt,
    String? branchId,
    bool? isSynced,
    String? productName,
    String? productBarcode,
    String? warehouseName,
    String? fromWarehouseName,
    String? toWarehouseName,
    String? createdByName,
  }) {
    return StockMovement(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      warehouseId: warehouseId ?? this.warehouseId,
      movementType: movementType ?? this.movementType,
      quantity: quantity ?? this.quantity,
      unitCost: unitCost ?? this.unitCost,
      totalValue: totalValue ?? this.totalValue,
      referenceType: referenceType ?? this.referenceType,
      referenceId: referenceId ?? this.referenceId,
      referenceNumber: referenceNumber ?? this.referenceNumber,
      fromWarehouseId: fromWarehouseId ?? this.fromWarehouseId,
      toWarehouseId: toWarehouseId ?? this.toWarehouseId,
      batchNumber: batchNumber ?? this.batchNumber,
      expiryDate: expiryDate ?? this.expiryDate,
      notes: notes ?? this.notes,
      reason: reason ?? this.reason,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      branchId: branchId ?? this.branchId,
      isSynced: isSynced ?? this.isSynced,
      productName: productName ?? this.productName,
      productBarcode: productBarcode ?? this.productBarcode,
      warehouseName: warehouseName ?? this.warehouseName,
      fromWarehouseName: fromWarehouseName ?? this.fromWarehouseName,
      toWarehouseName: toWarehouseName ?? this.toWarehouseName,
      createdByName: createdByName ?? this.createdByName,
    );
  }

  // Helper methods
  bool get isInbound => movementType == 'in';
  bool get isOutbound => movementType == 'out';
  bool get isTransfer => movementType == 'transfer';
  bool get isAdjustment => movementType == 'adjustment';
  bool get hasReference => referenceId != null && referenceId!.isNotEmpty;
  bool get hasBatch => batchNumber != null && batchNumber!.isNotEmpty;
  bool get hasExpiryDate => expiryDate != null;

  // Get movement type display text
  String get movementTypeText {
    switch (movementType) {
      case 'in':
        return 'إدخال';
      case 'out':
        return 'إخراج';
      case 'transfer':
        return 'نقل';
      case 'adjustment':
        return 'تسوية';
      default:
        return movementType;
    }
  }

  // Get reference type display text
  String get referenceTypeText {
    switch (referenceType) {
      case 'sale':
        return 'مبيعات';
      case 'purchase':
        return 'مشتريات';
      case 'adjustment':
        return 'تسوية';
      case 'transfer':
        return 'نقل';
      default:
        return referenceType ?? 'غير محدد';
    }
  }

  // Factory creators
  static StockMovement create({
    required String id,
    required String productId,
    required String warehouseId,
    required String movementType,
    required double quantity,
    required double unitCost,
    String? referenceType,
    String? referenceId,
    String? referenceNumber,
    String? fromWarehouseId,
    String? toWarehouseId,
    String? batchNumber,
    DateTime? expiryDate,
    String? notes,
    String? reason,
    required String createdBy,
    String? branchId,
  }) {
    return StockMovement(
      id: id,
      productId: productId,
      warehouseId: warehouseId,
      movementType: movementType,
      quantity: quantity,
      unitCost: unitCost,
      totalValue: quantity * unitCost,
      referenceType: referenceType,
      referenceId: referenceId,
      referenceNumber: referenceNumber,
      fromWarehouseId: fromWarehouseId,
      toWarehouseId: toWarehouseId,
      batchNumber: batchNumber,
      expiryDate: expiryDate,
      notes: notes,
      reason: reason,
      createdBy: createdBy,
      createdAt: DateTime.now(),
      branchId: branchId,
      isSynced: false,
    );
  }

  StockMovement markAsSynced() => copyWith(isSynced: true);

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StockMovement && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'StockMovement(id: $id, productId: $productId, movementType: $movementType, quantity: $quantity)';
  }
}

// Movement types constants
class StockMovementType {
  static const String inbound = 'in';
  static const String outbound = 'out';
  static const String transfer = 'transfer';
  static const String adjustment = 'adjustment';
}

// Reference types constants
class StockReferenceType {
  static const String sale = 'sale';
  static const String purchase = 'purchase';
  static const String adjustment = 'adjustment';
  static const String transfer = 'transfer';
  static const String opening = 'opening';
  static const String closing = 'closing';
}
