import 'package:tijari_tech/core/utils/app_utils.dart';
import 'package:tijari_tech/data/local/dao/customer_dao.dart';
import 'package:tijari_tech/data/local/dao/supplier_dao.dart';
import 'package:tijari_tech/data/local/dao/warehouse_dao.dart';
import 'package:tijari_tech/data/local/dao/user_dao.dart';
import 'package:tijari_tech/data/local/dao/unit_dao.dart';
import 'package:tijari_tech/data/local/dao/branch_dao.dart';
import 'package:tijari_tech/data/models/customer.dart';
import 'package:tijari_tech/data/models/supplier.dart';
import 'package:tijari_tech/data/models/warehouse.dart';
import 'package:tijari_tech/data/models/user.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'package:tijari_tech/data/models/branch.dart';
import 'package:tijari_tech/core/constants/app_constants.dart';

/// خدمة إدارة البيانات الافتراضية
/// تضمن وجود البيانات الأساسية المطلوبة لتشغيل النظام
class DefaultDataService {
  static final DefaultDataService _instance = DefaultDataService._internal();
  factory DefaultDataService() => _instance;
  DefaultDataService._internal();

  final CustomerDao _customerDao = CustomerDao();
  final SupplierDao _supplierDao = SupplierDao();
  final WarehouseDao _warehouseDao = WarehouseDao();
  final UserDao _userDao = UserDao();
  final UnitDao _unitDao = UnitDao();
  final BranchDao _branchDao = BranchDao();

  // IDs للبيانات الافتراضية
  static const String defaultCustomerId = 'default-customer';
  static const String defaultSupplierId = 'default-supplier';
  static const String defaultWarehouseId = 'default-warehouse';
  static const String defaultUserId = 'default-user';
  static const String defaultUnitId = 'default-unit';
  static const String defaultBranchId = 'default-branch';

  /// التأكد من وجود جميع البيانات الافتراضية
  Future<void> ensureDefaultDataExists() async {
    try {
      AppUtils.logInfo('بدء التحقق من البيانات الافتراضية...');

      await _ensureDefaultBranch();
      await _ensureDefaultUser();
      await _ensureDefaultWarehouse();
      await _ensureDefaultUnit();
      await _ensureDefaultCustomer();
      await _ensureDefaultSupplier();

      AppUtils.logInfo('تم التحقق من جميع البيانات الافتراضية بنجاح');
    } catch (e) {
      AppUtils.logError('خطأ في التحقق من البيانات الافتراضية', e);
      rethrow;
    }
  }

  /// التأكد من وجود الفرع الافتراضي
  Future<Branch> _ensureDefaultBranch() async {
    try {
      var branch = await _branchDao.getBranchById(defaultBranchId);
      if (branch == null) {
        AppUtils.logInfo('إنشاء الفرع الافتراضي...');
        branch = Branch.create(
          id: defaultBranchId,
          name: 'الفرع الرئيسي',
          address: 'العنوان الرئيسي للشركة',
          phone: '+966123456789',
          email: '<EMAIL>',
          isActive: true,
        );
        await _branchDao.insertBranch(branch);
        AppUtils.logInfo('تم إنشاء الفرع الافتراضي');
      }
      return branch;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء الفرع الافتراضي', e);
      rethrow;
    }
  }

  /// التأكد من وجود المستخدم الافتراضي
  Future<User> _ensureDefaultUser() async {
    try {
      var user = await _userDao.getUserById(defaultUserId);
      if (user == null) {
        AppUtils.logInfo('إنشاء المستخدم الافتراضي...');
        user = User.create(
          id: defaultUserId,
          name: 'مدير النظام',
          email: '<EMAIL>',
          phone: '+966123456789',
          role: 'admin',
          permissions: 'all',
          isActive: true,
          branchId: defaultBranchId,
        );
        await _userDao.insertUser(user);
        AppUtils.logInfo('تم إنشاء المستخدم الافتراضي');
      }
      return user;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء المستخدم الافتراضي', e);
      rethrow;
    }
  }

  /// التأكد من وجود المخزن الافتراضي
  Future<Warehouse> _ensureDefaultWarehouse() async {
    try {
      var warehouse = await _warehouseDao.getWarehouseById(defaultWarehouseId);
      if (warehouse == null) {
        AppUtils.logInfo('إنشاء المخزن الافتراضي...');
        warehouse = Warehouse.create(
          id: defaultWarehouseId,
          name: 'المخزن الرئيسي',
          location: 'الموقع الرئيسي',
          address: 'عنوان المخزن الرئيسي',
          managerId: defaultUserId,
          phone: '+966123456789',
          email: '<EMAIL>',
          isActive: true,
          isDefault: true,
          description: 'المخزن الرئيسي للشركة',
          warehouseType: 'main',
          capacity: 10000.0,
          branchId: defaultBranchId,
        );
        await _warehouseDao.insertWarehouse(warehouse);
        AppUtils.logInfo('تم إنشاء المخزن الافتراضي');
      }
      return warehouse;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء المخزن الافتراضي', e);
      rethrow;
    }
  }

  /// التأكد من وجود الوحدة الافتراضية
  Future<Unit> _ensureDefaultUnit() async {
    try {
      var unit = await _unitDao.getUnitById(defaultUnitId);
      if (unit == null) {
        AppUtils.logInfo('إنشاء الوحدة الافتراضية...');
        unit = Unit.create(
          id: defaultUnitId,
          name: 'قطعة',
          symbol: 'قطعة',
          abbreviation: 'ق',
          factor: 1.0,
          type: 'piece',
          isBaseUnit: true,
          isActive: true,
          description: 'الوحدة الافتراضية للعد',
        );
        await _unitDao.insertUnit(unit);
        AppUtils.logInfo('تم إنشاء الوحدة الافتراضية');
      }
      return unit;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء الوحدة الافتراضية', e);
      rethrow;
    }
  }

  /// التأكد من وجود العميل الافتراضي
  Future<Customer> _ensureDefaultCustomer() async {
    try {
      var customer = await _customerDao.getCustomerById(defaultCustomerId);
      if (customer == null) {
        AppUtils.logInfo('إنشاء العميل الافتراضي...');
        customer = Customer.create(
          id: defaultCustomerId,
          name: AppConstants.defaultCustomerName,
          phone: '',
          email: '',
          address: '',
          balance: 0.0,
          notes: 'العميل الافتراضي للمبيعات النقدية',
        );
        await _customerDao.insertCustomer(customer);
        AppUtils.logInfo('تم إنشاء العميل الافتراضي');
      }
      return customer;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء العميل الافتراضي', e);
      rethrow;
    }
  }

  /// التأكد من وجود المورد الافتراضي
  Future<Supplier> _ensureDefaultSupplier() async {
    try {
      var supplier = await _supplierDao.getSupplierById(defaultSupplierId);
      if (supplier == null) {
        AppUtils.logInfo('إنشاء المورد الافتراضي...');
        supplier = Supplier.create(
          id: defaultSupplierId,
          name: AppConstants.defaultSupplierName,
          phone: '',
          email: '',
          address: '',
          balance: 0.0,
          notes: 'المورد الافتراضي للمشتريات',
        );
        await _supplierDao.insertSupplier(supplier);
        AppUtils.logInfo('تم إنشاء المورد الافتراضي');
      }
      return supplier;
    } catch (e) {
      AppUtils.logError('خطأ في إنشاء المورد الافتراضي', e);
      rethrow;
    }
  }

  /// الحصول على العميل الافتراضي
  Future<Customer> getDefaultCustomer() async {
    var customer = await _customerDao.getCustomerById(defaultCustomerId);
    if (customer == null) {
      customer = await _ensureDefaultCustomer();
    }
    return customer;
  }

  /// الحصول على المورد الافتراضي
  Future<Supplier> getDefaultSupplier() async {
    var supplier = await _supplierDao.getSupplierById(defaultSupplierId);
    if (supplier == null) {
      supplier = await _ensureDefaultSupplier();
    }
    return supplier;
  }

  /// الحصول على المخزن الافتراضي
  Future<Warehouse> getDefaultWarehouse() async {
    var warehouse = await _warehouseDao.getWarehouseById(defaultWarehouseId);
    if (warehouse == null) {
      warehouse = await _ensureDefaultWarehouse();
    }
    return warehouse;
  }

  /// الحصول على المستخدم الافتراضي
  Future<User> getDefaultUser() async {
    var user = await _userDao.getUserById(defaultUserId);
    if (user == null) {
      user = await _ensureDefaultUser();
    }
    return user;
  }

  /// الحصول على الوحدة الافتراضية
  Future<Unit> getDefaultUnit() async {
    var unit = await _unitDao.getUnitById(defaultUnitId);
    if (unit == null) {
      unit = await _ensureDefaultUnit();
    }
    return unit;
  }

  /// الحصول على الفرع الافتراضي
  Future<Branch> getDefaultBranch() async {
    var branch = await _branchDao.getBranchById(defaultBranchId);
    if (branch == null) {
      branch = await _ensureDefaultBranch();
    }
    return branch;
  }
}
