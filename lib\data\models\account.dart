
// import 'package:json_annotation/json_annotation.dart';
// import '../../core/constants/database_constants.dart';

// part 'account.g.dart';

// /// نوع الحساب - العملاء أو الموردين
// /// Account Type - Customers or Suppliers
// enum AccountType {
//   customer('customer', 'عميل', 'العملاء'),
//   supplier('supplier', 'مورد', 'الموردين');

//   const AccountType(this.value, this.displayName, this.pluralName);
  
//   final String value;
//   final String displayName;
//   final String pluralName;

//   /// تحويل من النص إلى enum
//   static AccountType fromString(String value) {
//     return AccountType.values.firstWhere(
//       (type) => type.value == value,
//       orElse: () => AccountType.customer,
//     );
//   }
// }

// /// حالة الرصيد
// /// Balance Status
// enum BalanceStatus {
//   debt('debt', 'مدين', 'له رصيد'),
//   credit('credit', 'دائن', 'عليه رصيد'),
//   balanced('balanced', 'متوازن', 'رصيد صفر');

//   const BalanceStatus(this.value, this.displayName, this.description);
  
//   final String value;
//   final String displayName;
//   final String description;
// }

// /// نموذج الحساب الموحد للعملاء والموردين
// /// Unified Account Model for Customers and Suppliers
// @JsonSerializable()
// class Account {
//   final String id;
//   final String name;
//   final AccountType type;
//   /// رقم الهاتف (اختياري)
//   final String? phone;
  
//   /// البريد الإلكتروني (اختياري)
//   final String? email;
  
//   /// العنوان (اختياري)
//   final String? address;
  
//   /// الرصيد الحالي (موجب = مدين، سالب = دائن)
//   final double balance;
  
//   /// ملاحظات إضافية
//   final String? notes;
  
//   /// تاريخ الإنشاء
//   final DateTime? createdAt;
  
//   /// تاريخ آخر تحديث
//   final DateTime? updatedAt;
  
//   /// تاريخ الحذف (null إذا لم يتم الحذف)
//   final DateTime? deletedAt;
  
//   /// هل تم المزامنة مع الخادم
//   final bool isSynced;

//   const Account({
//     required this.id,
//     required this.name,
//     required this.type,
//     this.phone,
//     this.email,
//     this.address,
//     this.balance = 0.0,
//     this.notes,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.isSynced = false,
//   });

//   /// إنشاء من Map (قاعدة البيانات)
//   factory Account.fromMap(Map<String, dynamic> map) {
//     // تحديد نوع الحساب من الجدول
//     final tableName = map['_table_name'] as String?;
//     final AccountType accountType;
    
//     if (tableName == DatabaseConstants.tableCustomers) {
//       accountType = AccountType.customer;
//     } else if (tableName == DatabaseConstants.tableSuppliers) {
//       accountType = AccountType.supplier;
//     } else {
//       // محاولة تحديد النوع من البيانات
//       accountType = AccountType.fromString(map['type'] as String? ?? 'customer');
//     }

//     return Account(
//       id: map['id'] as String,
//       name: map['name'] as String,
//       phone: map['phone'] as String?,
//       email: map['email'] as String?,
//       address: map['address'] as String?,
//       balance: (map['balance'] as num?)?.toDouble() ?? 0.0,
//       notes: map['notes'] as String?,
//       type: accountType,
//       createdAt: map['created_at'] != null
//           ? DateTime.parse(map['created_at'] as String)
//           : null,
//       updatedAt: map['updated_at'] != null
//           ? DateTime.parse(map['updated_at'] as String)
//           : null,
//       deletedAt: map['deleted_at'] != null
//           ? DateTime.parse(map['deleted_at'] as String)
//           : null,
//       isSynced: (map['is_synced'] as int? ?? 0) == 1,
//     );
//   }

//   /// تحويل إلى Map (قاعدة البيانات)
//   Map<String, dynamic> toMap() {
//     return {
//       'id': id,
//       'name': name,
//       'phone': phone,
//       'email': email,
//       'address': address,
//       'balance': balance,
//       'notes': notes,
//       'created_at': createdAt?.toIso8601String(),
//       'updated_at': updatedAt?.toIso8601String(),
//       'deleted_at': deletedAt?.toIso8601String(),
//       'is_synced': isSynced ? 1 : 0,
//     };
//   }

//   /// JSON serialization
//   factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);
//   Map<String, dynamic> toJson() => _$AccountToJson(this);

//   /// إنشاء نسخة معدلة
//   Account copyWith({
//     String? id,
//     String? name,
//     String? phone,
//     String? email,
//     String? address,
//     double? balance,
//     String? notes,
//     AccountType? type,
//     DateTime? createdAt,
//     DateTime? updatedAt,
//     DateTime? deletedAt,
//     bool? isSynced,
//   }) {
//     return Account(
//       id: id ?? this.id,
//       name: name ?? this.name,
//       phone: phone ?? this.phone,
//       email: email ?? this.email,
//       address: address ?? this.address,
//       balance: balance ?? this.balance,
//       notes: notes ?? this.notes,
//       type: type ?? this.type,
//       createdAt: createdAt ?? this.createdAt,
//       updatedAt: updatedAt ?? this.updatedAt,
//       deletedAt: deletedAt ?? this.deletedAt,
//       isSynced: isSynced ?? this.isSynced,
//     );
//   }

//   /// المساواة والهاش
//   @override
//   bool operator ==(Object other) {
//     if (identical(this, other)) return true;
//     return other is Account && other.id == id;
//   }

//   @override
//   int get hashCode => id.hashCode;

//   /// تمثيل نصي
//   @override
//   String toString() {
//     return 'Account(id: $id, name: $name, type: ${type.displayName}, balance: $balance, isSynced: $isSynced)';
//   }

//   // ------------------------------------------------------------------
//   // Helper Methods - الطرق المساعدة
//   // ------------------------------------------------------------------

//   /// هل الحساب محذوف؟
//   bool get isDeleted => deletedAt != null;
  
//   /// هل الحساب نشط؟
//   bool get isActive => !isDeleted;
  
//   /// هل يوجد رقم هاتف؟
//   bool get hasPhone => phone != null && phone!.isNotEmpty;
  
//   /// هل يوجد بريد إلكتروني؟
//   bool get hasEmail => email != null && email!.isNotEmpty;
  
//   /// هل يوجد عنوان؟
//   bool get hasAddress => address != null && address!.isNotEmpty;
  
//   /// هل يوجد ملاحظات؟
//   bool get hasNotes => notes != null && notes!.isNotEmpty;
  
//   /// هل الحساب مدين (له رصيد)؟
//   bool get hasDebt => balance > 0;
  
//   /// هل الحساب دائن (عليه رصيد)؟
//   bool get hasCredit => balance < 0;
  
//   /// هل الرصيد متوازن؟
//   bool get isBalanced => balance == 0;

//   /// الحصول على حالة الرصيد
//   BalanceStatus get balanceStatus {
//     if (balance > 0) return BalanceStatus.debt;
//     if (balance < 0) return BalanceStatus.credit;
//     return BalanceStatus.balanced;
//   }

//   /// الحصول على القيمة المطلقة للرصيد
//   double get absoluteBalance => balance.abs();

//   /// هل هو عميل؟
//   bool get isCustomer => type == AccountType.customer;
  
//   /// هل هو مورد؟
//   bool get isSupplier => type == AccountType.supplier;

//   // ------------------------------------------------------------------
//   // Factory Methods - طرق الإنشاء
//   // ------------------------------------------------------------------

//   /// إنشاء حساب جديد
//   static Account create({
//     required String id,
//     required String name,
//     required AccountType type,
//     String? phone,
//     String? email,
//     String? address,
//     double balance = 0.0,
//     String? notes,
//   }) {
//     final now = DateTime.now();
//     return Account(
//       id: id,
//       name: name,
//       type: type,
//       phone: phone,
//       email: email,
//       address: address,
//       balance: balance,
//       notes: notes,
//       createdAt: now,
//       updatedAt: now,
//       isSynced: false,
//     );
//   }

//   // ------------------------------------------------------------------
//   // Business Methods - الطرق التجارية
//   // ------------------------------------------------------------------

//   /// تحديث الحساب مع طابع زمني جديد
//   Account update({
//     String? name,
//     String? phone,
//     String? email,
//     String? address,
//     double? balance,
//     String? notes,
//   }) {
//     return copyWith(
//       name: name ?? this.name,
//       phone: phone ?? this.phone,
//       email: email ?? this.email,
//       address: address ?? this.address,
//       balance: balance ?? this.balance,
//       notes: notes ?? this.notes,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// إضافة مبلغ للرصيد
//   Account addToBalance(double amount) {
//     return copyWith(
//       balance: balance + amount,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// خصم مبلغ من الرصيد
//   Account subtractFromBalance(double amount) {
//     return copyWith(
//       balance: balance - amount,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// تصفير الرصيد
//   Account clearBalance() {
//     return copyWith(
//       balance: 0.0,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// تحديد الرصيد
//   Account setBalance(double newBalance) {
//     return copyWith(
//       balance: newBalance,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// وضع علامة الحذف
//   Account markAsDeleted() {
//     return copyWith(
//       deletedAt: DateTime.now(),
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// وضع علامة المزامنة
//   Account markAsSynced() {
//     return copyWith(isSynced: true);
//   }

//   /// إزالة الهاتف
//   Account removePhone() {
//     return copyWith(
//       phone: null,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// إزالة البريد الإلكتروني
//   Account removeEmail() {
//     return copyWith(
//       email: null,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// إزالة العنوان
//   Account removeAddress() {
//     return copyWith(
//       address: null,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }

//   /// إزالة الملاحظات
//   Account removeNotes() {
//     return copyWith(
//       notes: null,
//       updatedAt: DateTime.now(),
//       isSynced: false,
//     );
//   }
// }

