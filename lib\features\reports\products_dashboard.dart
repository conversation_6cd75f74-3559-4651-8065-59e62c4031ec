import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:tijari_tech/core/utils/error_handler.dart';
import 'package:tijari_tech/data/models/product.dart';
import 'package:tijari_tech/data/models/category.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unit_provider.dart';
import '../../widgets/main_layout.dart';

class ProductsDashboard extends ConsumerStatefulWidget {
  const ProductsDashboard({super.key});

  @override
  ConsumerState<ProductsDashboard> createState() => _ProductsDashboardState();
}

class _ProductsDashboardState extends ConsumerState<ProductsDashboard> {
  String _selectedPeriod = 'month'; // week, month, quarter, year
  DateTime _selectedDate = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'لوحة معلومات المنتجات',
      child: RefreshIndicator(
        onRefresh: _refreshData,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Period selector
              _buildPeriodSelector(),
              SizedBox(height: 16.h),

              // Key metrics cards
              _buildKeyMetrics(),
              SizedBox(height: 24.h),

              // Charts section
              _buildChartsSection(),
              SizedBox(height: 24.h),

              // Tables section
              _buildTablesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            Text(
              'الفترة الزمنية:',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 16.w),

            Expanded(
              child: SegmentedButton<String>(
                segments: const [
                  ButtonSegment(value: 'week', label: Text('أسبوع')),
                  ButtonSegment(value: 'month', label: Text('شهر')),
                  ButtonSegment(value: 'quarter', label: Text('ربع سنة')),
                  ButtonSegment(value: 'year', label: Text('سنة')),
                ],
                selected: {_selectedPeriod},
                onSelectionChanged: (Set<String> selection) {
                  setState(() {
                    _selectedPeriod = selection.first;
                  });
                },
              ),
            ),

            SizedBox(width: 16.w),

            // Date picker
            OutlinedButton.icon(
              onPressed: _selectDate,
              icon: const Icon(Icons.calendar_today),
              label: Text(_formatDate(_selectedDate)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyMetrics() {
    final productsStatsAsync = ref.watch(productsStatsProvider);
    final categoriesStatsAsync = ref.watch(categoriesStatsProvider);
    final unitsStatsAsync = ref.watch(unitsStatsProvider);
    final lowStockAsync = ref.watch(lowStockProductsProvider);
    final outOfStockAsync = ref.watch(outOfStockProductsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المؤشرات الرئيسية',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: 3.0,
          children: [
            // Total products
            productsStatsAsync.when(
              data: (stats) => _buildMetricCard(
                title: 'إجمالي المنتجات',
                value: '${stats['total_products'] ?? 0}',
                subtitle: '${stats['active_products'] ?? 0} نشط',
                icon: Icons.inventory_2,
                color: AppColors.primary,
              ),
              loading: () => _buildLoadingMetricCard(),
              error: (error, stack) => _buildErrorMetricCard(),
            ),

            // Total categories
            categoriesStatsAsync.when(
              data: (stats) => _buildMetricCard(
                title: 'إجمالي الفئات',
                value: '${stats['total_categories'] ?? 0}',
                subtitle: '${stats['main_categories'] ?? 0} رئيسية',
                icon: Icons.category,
                color: Colors.green,
              ),
              loading: () => _buildLoadingMetricCard(),
              error: (error, stack) => _buildErrorMetricCard(),
            ),

            // Low stock products
            lowStockAsync.when(
              data: (products) => _buildMetricCard(
                title: 'مخزون منخفض',
                value: '${products.length}',
                subtitle: 'منتج يحتاج تجديد',
                icon: Icons.warning,
                color: Colors.orange,
              ),
              loading: () => _buildLoadingMetricCard(),
              error: (error, stack) => _buildErrorMetricCard(),
            ),

            // Out of stock products
            outOfStockAsync.when(
              data: (products) => _buildMetricCard(
                title: 'نفد المخزون',
                value: '${products.length}',
                subtitle: 'منتج غير متوفر',
                icon: Icons.error,
                color: Colors.red,
              ),
              loading: () => _buildLoadingMetricCard(),
              error: (error, stack) => _buildErrorMetricCard(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24.r,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    value,
                    style: AppTextStyles.titleLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingMetricCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  Widget _buildErrorMetricCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Center(
          child: Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 32.r,
          ),
        ),
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرسوم البيانية',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),

        Row(
          children: [
            // Products by category chart
            Expanded(
              child: _buildCategoryDistributionChart(),
            ),
            SizedBox(width: 16.w),

            // Stock status chart
            Expanded(
              child: _buildStockStatusChart(),
            ),
          ],
        ),
        SizedBox(height: 16.h),

        // Price distribution chart
        _buildPriceDistributionChart(),
      ],
    );
  }

  Widget _buildCategoryDistributionChart() {
    final categoriesStatsAsync = ref.watch(mostUsedCategoriesProvider);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع المنتجات حسب الفئة',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            categoriesStatsAsync.when(
              data: (categories) => SizedBox(
                height: 200.h,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(categories),
                    centerSpaceRadius: 40.r,
                    sectionsSpace: 2,
                  ),
                ),
              ),
              loading: () => SizedBox(
                height: 200.h,
                child: const LoadingWidget(),
              ),
              error: (error, stack) => SizedBox(
                height: 200.h,
                child: ErrorDisplayWidget(
                  error: error.toString(),
                  onRetry: () => ref.refresh(mostUsedCategoriesProvider),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockStatusChart() {
    final lowStockAsync = ref.watch(lowStockProductsProvider);
    final outOfStockAsync = ref.watch(outOfStockProductsProvider);
    final productsStatsAsync = ref.watch(productsStatsProvider);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'حالة المخزون',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            // يراجع بشكل كامل
            SizedBox(height: 16.h),
            Consumer(
              builder: (context, ref, child) {
                final asyncCombined = AsyncValue.data([
                  ref.watch(lowStockProductsProvider),
                  ref.watch(outOfStockProductsProvider),
                  ref.watch(productsStatsProvider),
                ]);

                return asyncCombined.when(
                  data: (values) {
                    final lowStock = values[0] as List<Product>;
                    final outOfStock = values[1] as List<Product>;
                    final stats = values[2] as Map<String, dynamic>;

                    final totalProducts = stats['total_products'] as int? ?? 0;
                    final availableProducts =
                        totalProducts - lowStock.length - outOfStock.length;

                    final data = [
                      {
                        'status': 'متوفر',
                        'count': availableProducts,
                        'color': Colors.green
                      },
                      {
                        'status': 'مخزون منخفض',
                        'count': lowStock.length,
                        'color': Colors.orange
                      },
                      {
                        'status': 'نفد المخزون',
                        'count': outOfStock.length,
                        'color': Colors.red
                      },
                    ];

                    return SizedBox(
                      height: 200.h,
                      child: BarChart(
                        BarChartData(
                          alignment: BarChartAlignment.spaceAround,
                          maxY: data
                                  .map((e) => e['count'] as int)
                                  .reduce((a, b) => a > b ? a : b)
                                  .toDouble() *
                              1.2,
                          barGroups: data.asMap().entries.map((entry) {
                            final index = entry.key;
                            final item = entry.value;
                            return BarChartGroupData(
                              x: index,
                              barRods: [
                                BarChartRodData(
                                  toY: (item['count'] as int).toDouble(),
                                  color: item['color'] as Color,
                                  width: 40.w,
                                  borderRadius: BorderRadius.circular(4.r),
                                ),
                              ],
                            );
                          }).toList(),
                          titlesData: FlTitlesData(
                            leftTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: true),
                            ),
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  final index = value.toInt();
                                  if (index >= 0 && index < data.length) {
                                    return Text(
                                      data[index]['status'] as String,
                                      style: AppTextStyles.bodySmall,
                                    );
                                  }
                                  return const Text('');
                                },
                              ),
                            ),
                            topTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                            rightTitles: const AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                          ),
                          gridData: const FlGridData(show: false),
                          borderData: FlBorderData(show: false),
                        ),
                      ),
                    );
                  },
                  loading: () => SizedBox(
                    height: 200.h,
                    child: const LoadingWidget(),
                  ),
                  error: (error, stack) => SizedBox(
                    height: 200.h,
                    child: ErrorDisplayWidget(
                      error: error.toString(),
                      onRetry: () {
                        ref.refresh(lowStockProductsProvider);
                        ref.refresh(outOfStockProductsProvider);
                        ref.refresh(productsStatsProvider);
                      },
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceDistributionChart() {
    final productsStatsAsync = ref.watch(productsStatsProvider);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع الأسعار',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            productsStatsAsync.when(
              data: (stats) => SizedBox(
                height: 200.h,
                child: _buildPriceRangeChart(stats),
              ),
              loading: () => SizedBox(
                height: 200.h,
                child: const LoadingWidget(),
              ),
              error: (error, stack) => SizedBox(
                height: 200.h,
                child: ErrorDisplayWidget(
                  error: error.toString(),
                  onRetry: () => ref.refresh(productsStatsProvider),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRangeChart(Map<String, dynamic> stats) {
    // Create price ranges based on min and max prices
    // final minPrice = (stats['min_price'] as num?)?.toDouble() ?? 0;
    final maxPrice = (stats['max_price'] as num?)?.toDouble() ?? 100;
    // final avgPrice = (stats['avg_selling_price'] as num?)?.toDouble() ?? 50;

    // Mock data for price distribution
    final priceRanges = [
      {'range': '0-${(maxPrice * 0.2).toInt()}', 'count': 15},
      {
        'range': '${(maxPrice * 0.2).toInt()}-${(maxPrice * 0.4).toInt()}',
        'count': 25
      },
      {
        'range': '${(maxPrice * 0.4).toInt()}-${(maxPrice * 0.6).toInt()}',
        'count': 30
      },
      {
        'range': '${(maxPrice * 0.6).toInt()}-${(maxPrice * 0.8).toInt()}',
        'count': 20
      },
      {'range': '${(maxPrice * 0.8).toInt()}+', 'count': 10},
    ];

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: true),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < priceRanges.length) {
                  return Text(
                    priceRanges[index]['range'] as String,
                    style: AppTextStyles.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: priceRanges.asMap().entries.map((entry) {
              return FlSpot(
                entry.key.toDouble(),
                (entry.value['count'] as int).toDouble(),
              );
            }).toList(),
            isCurved: true,
            color: AppColors.primary,
            barWidth: 3,
            dotData: const FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  Widget _buildTablesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الجداول التفصيلية',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            // Low stock products table
            Expanded(
              child: _buildLowStockTable(),
            ),
            SizedBox(width: 16.w),

            // Top categories table
            Expanded(
              child: _buildTopCategoriesTable(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLowStockTable() {
    final lowStockAsync = ref.watch(lowStockProductsProvider);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المنتجات ذات المخزون المنخفض',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            lowStockAsync.when(
              data: (products) => SizedBox(
                height: 300.h,
                child: ListView.builder(
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return ListTile(
                      title: Text(product.getDisplayName()),
                      subtitle: Text(
                          'المخزون: ${product.currentStock?.toStringAsFixed(0) ?? '0'}'),
                      trailing: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'منخفض',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              loading: () => SizedBox(
                height: 300.h,
                child: const LoadingWidget(),
              ),
              error: (error, stack) => SizedBox(
                height: 300.h,
                child: ErrorDisplayWidget(
                  error: error.toString(),
                  onRetry: () => ref.refresh(lowStockProductsProvider),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCategoriesTable() {
    final categoriesAsync = ref.watch(mostUsedCategoriesProvider);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'أكثر الفئات استخداماً',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12.h),
            categoriesAsync.when(
              data: (categories) => SizedBox(
                height: 300.h,
                child: ListView.builder(
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return ListTile(
                      title: Text(category.nameAr),
                      subtitle: Text('${category.productCount ?? 0} منتج'),
                      trailing: CircularProgressIndicator(
                        value: (category.productCount ?? 0) / 100,
                        backgroundColor: Colors.grey[300],
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.primary),
                      ),
                    );
                  },
                ),
              ),
              loading: () => SizedBox(
                height: 300.h,
                child: const LoadingWidget(),
              ),
              error: (error, stack) => SizedBox(
                height: 300.h,
                child: ErrorDisplayWidget(
                  error: error.toString(),
                  onRetry: () => ref.refresh(mostUsedCategoriesProvider),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(List<Category> categories) {
    final colors = [
      AppColors.primary,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
      Colors.blue,
      Colors.teal,
      Colors.pink,
    ];

    return categories.take(8).toList().asMap().entries.map((entry) {
      final index = entry.key;
      final category = entry.value;
      final productCount = category.productCount ?? 0;

      return PieChartSectionData(
        color: colors[index % colors.length],
        value: productCount.toDouble(),
        title: '${category.nameAr}\n$productCount',
        radius: 60.r,
        titleStyle: AppTextStyles.bodySmall.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      );
    }).toList();
  }

  Future<void> _refreshData() async {
    ref.refresh(productsStatsProvider);
    ref.refresh(categoriesStatsProvider);
    ref.refresh(unitsStatsProvider);
    ref.refresh(lowStockProductsProvider);
    ref.refresh(outOfStockProductsProvider);
    ref.refresh(mostUsedCategoriesProvider);
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
